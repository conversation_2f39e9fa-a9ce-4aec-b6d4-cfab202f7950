package commands

import (
	"context"
	"fmt"
	"strings"

	"telegram-bot-api/internal/service"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
)

// SupportCommandResponse represents the response for the support command
type SupportCommandResponse struct {
	ChatID         int64                          `json:"chat_id"`
	Text           string                         `json:"text"`
	ParseMode      string                         `json:"parse_mode,omitempty"`
	InlineKeyboard *tgbotapi.InlineKeyboardMarkup `json:"reply_markup,omitempty"`
}

// GetChatID returns the chat ID
func (r *SupportCommandResponse) GetChatID() int64 {
	return r.ChatID
}

// GetText returns the response text
func (r *SupportCommandResponse) GetText() string {
	return r.Text
}

// GetReplyMarkup returns the keyboard markup
func (r *SupportCommandResponse) GetReplyMarkup() *tgbotapi.InlineKeyboardMarkup {
	return r.InlineKeyboard
}

// GetParseMode returns the parse mode
func (r *SupportCommandResponse) GetParseMode() string {
	return r.ParseMode
}

// HandleSupportCommand processes the /support command and returns support information
func HandleSupportCommand(ctx context.Context, update *tgbotapi.Update) (CommandResponse, error) {
	chat := update.Message.Chat
	userID := update.Message.From.ID

	g.Log().Infof(ctx, "Handling /support command from user %d", userID)

	// Get tenant information to retrieve customer support contact
	tenantInfo, err := service.Admin().GetTenantInfo(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get tenant info: %v", err)
		return &BaseResponse{
			ChatID: chat.ID,
			Text:   "Failed to get support information. Please try again later.",
		}, nil
	}

	// Get customer support contact from tenant info
	customer := tenantInfo.Customer
	if customer == "" {
		g.Log().Warningf(ctx, "No customer support contact configured for tenant %d", tenantInfo.TenantId)
		customer = "https://t.me/defaultsupport" // fallback
	}

	// Get user display name
	var userName string
	if update.Message.From.FirstName != "" {
		userName = update.Message.From.FirstName
	} else if update.Message.From.UserName != "" {
		userName = update.Message.From.UserName
	} else {
		userName = "用户" // fallback
	}

	i18n := service.I18n().Instance()

	// Build support text with user name
	supportText := i18n.Tf(ctx, "SupportCenterText", userName)

	// Create inline keyboard with support options
	var keyboard tgbotapi.InlineKeyboardMarkup

	// Build keyboard rows
	var rows [][]tgbotapi.InlineKeyboardButton

	// Ensure proper URL format for customer support
	var customerURL string
	if strings.HasPrefix(customer, "http://") || strings.HasPrefix(customer, "https://") {
		customerURL = customer
	} else if strings.HasPrefix(customer, "@") {
		// Convert @username to URL
		customerURL = fmt.Sprintf("https://t.me/%s", strings.TrimPrefix(customer, "@"))
	} else {
		// Assume it's a username without @
		customerURL = fmt.Sprintf("https://t.me/%s", customer)
	}

	// Contact support button
	contactRow := []tgbotapi.InlineKeyboardButton{
		tgbotapi.NewInlineKeyboardButtonURL(i18n.T(ctx, "SupportLiveChatButton"), customerURL),
	}
	rows = append(rows, contactRow)

	// Create keyboard with all rows
	keyboard = tgbotapi.NewInlineKeyboardMarkup(rows...)

	return &SupportCommandResponse{
		ChatID:         chat.ID,
		Text:           supportText,
		ParseMode:      "HTML",
		InlineKeyboard: &keyboard,
	}, nil
}
