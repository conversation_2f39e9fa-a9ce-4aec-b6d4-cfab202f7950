# Account Statement
AccountStatementSelectTokenPrompt = "请选择要查询账单的代币："
ErrorFetchingData = "获取数据时出错，请稍后再试。"
AccountStatementTitle = "账户账单"
AccountStatementNoRecords = "没有找到相关交易记录。"
TransactionTypeLabel = "类型"
TransactionAmountLabel = "金额"
TransactionBalanceLabel = "余额"
TransactionMemoLabel = "备注"
# Transaction Types (Add more as needed based on transactions.type column)
TransactionType_transfer = "转账"
TransactionType_deposit = "充值"
TransactionType_withdrawal = "提现"
TransactionType_red_packet = "红包"
TransactionType_payment = "支付"
TransactionType_commission = "佣金"
TransactionType_system_adjust = "系统调整"
TransactionType_withdraw="提现"
TransactionType_transfer_out="转账支出"
TransactionType_transfer_in="转账收入"
TransactionType_swap="兑换"
TransactionType_swap_in="兑换收入"
TransactionType_swap_out="兑换支出"

# 纯数字查询相关
NoTokensSupportAmount = "❌ 没有代币支持该转账金额精度"
NoTokensSupportAmountReceive = "❌ 没有代币支持该收款金额精度"
NumericTransferTitle = "转账 %s %s"
NumericTransferDesc = "使用 %s 转账"
NumericReceiveTitle = "收款 %s %s"
NumericReceiveDesc = "创建 %s 收款请求"
insufficientBalanceAllTokens = "❌ 您在所有代币中的余额都不足"

# Withdraw
WithdrawAddressAddSuccess = "✅ 地址添加成功！"

# Admin Center
AdminCenter = "管理员中心"
AdminWelcome = "你好，管理员"
AdminPermissionDenied = "您没有管理员权限"
AdminPermissionError = "权限验证失败"
AdminSystemError = "系统错误，请稍后重试"
AdminCenterTitle = "【管理员中心】"

# Balance adjustment messages
AdminUserNotFound = "❌ 用户不存在，请检查用户ID"
AdminInvalidUserID = "❌ 无效的用户ID格式"
AdminUsernameNotSupported = "❌ 暂不支持通过用户名查询，请使用用户ID"
AdminInvalidBalanceFormat = "❌ 输入格式错误\n\n正确格式：\n用户ID 金额 [说明]\n\n例如：123456 100 充值补偿"
AdminInvalidAmount = "❌ 无效的金额格式"
AdminDefaultIncreaseReason = "管理员调整"
AdminDefaultDecreaseReason = "管理员调整"
AdminAdjustmentFailed = "❌ 调整失败"
AdminBalanceIncreaseSuccess = "✅ 成功！\n\n%s的CNY增加了%s\n当前钱包余额：%s"
AdminBalanceDecreaseSuccess = "✅ 成功！\n\n%s的CNY减少了%s\n当前钱包余额：%s"

# Flow requirement messages
AdminInvalidFlowFormat = "❌ 输入格式错误\n\n正确格式：\n用户ID 流水金额 [说明]\n\n例如：123456 500 活动奖励"
AdminInvalidFlowFormatStrict = "❌ 输入格式错误\n\n正确格式：\n用户ID 流水金额\n或\n@用户名 流水金额\n\n例如：\n7675700937 100\n@agoukuaile 100"
AdminDefaultFlowIncreaseReason = "管理员调整"
AdminDefaultFlowDecreaseReason = "管理员调整"
AdminFlowIncreaseSuccess = "✅ 成功！\n\n%s的流水要求增加了%s"
AdminFlowDecreaseSuccess = "✅ 成功！\n\n%s的流水要求减少了%s"

# Admin Buttons
AdminButtonManualBalance = "手动增减余额"
AdminButtonFlowRequirements = "增减流水要求"
AdminButtonPlatformStats = "平台统计数据"
AdminButtonPersonalStats = "个人统计数据"
AdminButtonDailyReport = "查指定日期范围每日存取款"
AdminButtonDepositLogs = "平台存款日志"
AdminButtonWithdrawLogs = "平台取款日志"
AdminButtonMerchantInfo = "商户信息"
AdminButtonBack = "返回"
AdminButtonBackToAdmin = "返回管理员中心"
AdminButtonRefresh = "刷新"
AdminButtonPrev = "上一页"
AdminButtonNext = "下一页"

# Admin Functions
AdminManualBalanceInstruction = "请回复用户的Telegram ID或者用户名，然后加上需要增或者减少的金额，以空格分隔。\n\n普通余额调整示例：@agoukuaile 100\n彩金调整示例：@agoukuaile 100 彩金"
AdminFlowRequirementsInstruction = "请回复用户的Telegram ID或者用户名，然后加上需要增或者减少的流水要求金额，以空格分隔。\n\n例如：@agoukuaile 100"

# Admin Input Validation Messages
AdminFormatError = "❌ 您回复的信息格式错误"
AdminBalanceFormatErrorStrict = "❌ 输入格式错误\n\n正确格式：\n用户ID 金额\n@用户名 金额\n用户ID 金额 彩金\n@用户名 金额 彩金\n\n例如：\n7675700937 100\n@agoukuaile 100\n7675700937 100 彩金"
AdminInvalidOperationType = "❌ 操作类型错误\n\n支持的操作类型：\n• 不填写（普通余额调整）\n• 彩金（彩金流水调整）\n\n例如：\n7675700937 100\n7675700937 100 彩金"
AdminUserNotFoundError = "❌ 用户不存在"
AdminInsufficientBalanceError = "❌ 操作失败：用户的钱包余额不足\n\n%s 的钱包余额：%s CNY"

# Platform Stats
AdminPlatformStatsPlaceholder = "平台统计数据功能开发中..."
AdminPlatformStatsError = "获取平台统计数据失败"
AdminPlatformStatsNoData = "暂无统计数据"
AdminPlatformStatsFormat = "📊 平台统计数据\n\n👥 总用户数: %d\n💰 总存款: %s\n💸 总提现: %s\n💎 总佣金: %s"

# Personal Stats
AdminPersonalStatsPlaceholder = "个人统计数据功能开发中..."
AdminPersonalStatsNoData = "暂无个人统计数据"
AdminPersonalStatsFormat = "👤 个人统计数据\n\n用户ID: %d\n总金额: %s"
AdminPersonalStatsHeader = "个人统计查询"
AdminPersonalStatsSearchInstructions = "请输入用户的Telegram ID或用户名进行查询"
AdminPersonalStatsSearchExamples = "查询示例"
AdminPersonalStatsSearchByTelegramID = "通过Telegram ID查询"
AdminPersonalStatsSearchByUsername = "通过用户名查询"
AdminPersonalStatsUserNotFound = "❌ 用户不存在"
AdminPersonalStatsError = "❌ 查询失败，请稍后重试"
AdminPersonalStatsBanSuccess = "✅ 用户已被禁用"
AdminPersonalStatsUnbanSuccess = "✅ 用户已被启用"

# Personal Stats Detail Fields
AdminPersonalStatsDetailHeader = "个人详细统计"
AdminUserInfo = "用户信息"
AdminUserID = "用户ID"
AdminTelegramID = "Telegram ID"
AdminUsername = "用户名"
AdminUserStatus = "用户状态"
AdminUserStatusActive = "正常"
AdminUserStatusBanned = "已禁用"
AdminBalanceInfo = "余额信息"
AdminFinancialInfo = "财务信息"
AdminTotalWithdrawals = "总提现"
AdminNetProfitLoss = "净盈亏"
AdminActivityInfo = "活动信息"
AdminRegisteredAt = "注册时间"
AdminLastActiveAt = "最后活跃"
AdminNever = "从未"

# Flow Stats
AdminFlowStatsHeader = "流水统计"
AdminFlowSummary = "流水汇总"
AdminTotalIn = "总流入"
AdminTotalOut = "总流出"
AdminFlowPeriodToday = "今日"
AdminFlowPeriodYesterday = "昨日"
AdminFlowPeriodWeek = "本周"
AdminFlowPeriodMonth = "本月"

# Button Labels
AdminButtonFlowToday = "今日流水"
AdminButtonFlowYesterday = "昨日流水"
AdminButtonFlowWeek = "本周流水"
AdminButtonFlowMonth = "本月流水"
AdminButtonBanUser = "禁用用户"
AdminButtonUnbanUser = "启用用户"
AdminButtonBackToSearch = "返回搜索"

# Daily Report
AdminDailyReportPlaceholder = "查指定日期范围每日存取款功能开发中..."
AdminDailyReportNoData = "暂无日报数据"
AdminDailyReportFormat = "📅 日报数据 (%s)\n\n💰 当日存款: %s\n💸 当日提现: %s"

# Logs
AdminDepositLogsPlaceholder = "平台存款日志功能开发中..."
AdminDepositLogsNoData = "暂无存款日志"
AdminDepositLogsHeader = "📥 平台存款日志"
AdminWithdrawLogsPlaceholder = "平台取款日志功能开发中..."
AdminWithdrawLogsNoData = "暂无取款日志"
AdminWithdrawLogsHeader = "📤 平台取款日志"
AdminLogsPaginationInfo = "页码: %d | 总记录: %d"

# Merchant Info
AdminMerchantInfoPlaceholder = "商户信息功能开发中..."
AdminMerchantInfoError = "获取商户信息失败"
AdminMerchantInfoNoData = "暂无商户信息"
AdminMerchantInfoFormat = "🏪 商户信息\n\n商户ID: %d\n商户名称: %s"

# Navigation
# Note: Back to main menu now uses DeleteAndSendMainMenuResponse which automatically sends main menu


# 中文翻译文件 (zh-CN.toml)

# 开始命令回复文本
StartCommandText = '''👋Hi, %s 🆔 %s
——-——-——-——-——-——-——
💰钱包余额: %s
🕰当前时间: %s'''
# ----
# OK借贷: @iNFT
# 按钮文本
DepositButton = "💰 充值"
WithdrawButton = "💸 提现"
TransferButton = "⬆️ 转账"
ReceiveButton = "⬇️ 收钱"
RedPacketButton = "🧧 红包"
SwapButton = "🔄 闪兑"
ProfileButton = "👤 个人中心"
LanguageButton = "🌐 语言"

# 新菜单按钮
EvolutionGameButton = "🎮 Evolution游戏"
PPGameButton = "🎮 PP游戏"
PGElectronicsButton = "🎮 PG电子"
OfficialGroupButton = "👥 官方群组"
CustomerSupportButton = "🎧 客服支持"
DepositWithdrawButton = "💰 充值提现"
InviteFriendsButton = "👥 邀请好友"

# 邀请好友页面
InviteFriendsTitle = "👋 邀请好友加入"
InviteLinkLabel = "🔗 您的专属邀请链接:"
InviteRewardsTitle = "📊 邀请奖励:"
DirectRewardText = "- 直接下级投注流水的%s%%作为您的佣金"
IndirectRewardText = "- 间接下级投注流水的%s%%作为您的佣金"
RewardUnlimitedText = "- 无上限，终身有效"
InviteStatsTitle = "📈 邀请统计"
InvitedUsersCount = "- 已邀请用户: %d 人"
TotalCommission = "- 累计获得佣金: %.2f CNY"
ViewDirectButton = "👥 直接下级"
ViewIndirectButton = "👥 间接下级"

# 邀请分享内联消息
InviteFriendsDescription = "分享此邀请链接给朋友，获得他们下注的返佣奖励"
ErrorGettingUserData = "获取用户数据失败"
DefaultInvitePromoText = "这里是文案文案，文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案，等待产品Eriq提供"
IAmPrefix = "我是"
InviteBenefit1 = "无需注册，打开即可游戏！"
InviteBenefit2 = "好打好爆，一炮上万！"
InviteBenefit3 = "邀请好友享受投注佣金！"
InviteBenefit4 = "多种支付方式，安全便捷！"
StartGameButton = "开始游戏"

ShareRewardsButton = "🎁 分享领奖"
SendRedPacketButton = "🧧 发红包"

# 充值提现页面
DepositWithdrawTitle = "充值提现"
BalanceLabel = "余额"
DepositActionButton = "存款"
WithdrawActionButton = "取款"
TransactionHistoryButton = "历史账单"
WithdrawHistoryButton = "取款历史"

# 新充值页面
DepositSelectMethodTitle = "请选择您的存款方式"
DepositExchangeRate = "当前汇率 1 %s ≈ %s %s"
DepositExchangeRateHint = "请使用当前汇率进行充值"
DepositRewardLabel = "存款奖励"
DepositFlowRequirementLabel = "流水要求"
DepositManualPaymentButton = "👤 支付宝微信(人工 %s起)"
DepositRewardSettingsButton = "存款奖励设置"

# 存款奖励设置
DepositRewardSettingsTitle = "存款奖励设置"
RewardLabel = "奖励"
FlowRequirementLabel = "流水要求"
NoRewardLabel = "无奖励"
DepositRewardSelectedMessage = "✅ 您已选择 %s%% 存款奖励"

# 通用消息
ComingSoonMessage = "该功能正在开发中，敬请期待！"

# 游戏显示
GamePackageBalance = "包金额"
GameBalance = "游戏余额"
CurrentTime = "当前时间"
TransferToGame = "转入星力"
TransferToWallet = "转回钱包"
WebVersion = "网页版"
StartGame = "开始游戏"
ReturnHome = "返回首页"
TransferInTitle = "转入功能"
TransferOutTitle = "转出功能"
InsufficientGameBalance = "游戏余额不足"

# 语言选择
SelectLanguage = "请选择您的语言："
LanguageChanged = "🌐 语言已更改为中文"
BackButton = "返回"
BackToMainMenu = "◀️ 返回主菜单"
LanguageSetSuccess = "✅ 语言已成功设置为 %s"

# 权限相关错误信息
DepositPermissionDenied = "❌ 您的账户暂时无法使用充值功能"
WithdrawPermissionDenied = "❌ 您的账户暂时无法使用提现功能"
WithdrawNoPaymentPassword = "❌ 请先设置支付密码才能使用提现功能"
TransferPermissionDenied = "❌ 您的账户暂时无法使用转账功能"
ReceivePermissionDenied = "❌ 您的账户暂时无法使用收款功能"
RedPacketPermissionDenied = "❌ 您的账户暂时无法使用红包功能"
SwapPermissionDenied = "❌ 您的账户暂时无法使用闪兑功能"

# 系统维护消息
DepositSystemMaintenance = "🔧 充值功能正在维护中，请稍后再试。"
WithdrawSystemMaintenance = "🔧 提现功能正在维护中，请稍后再试。"
TransferSystemMaintenance = "🔧 转账功能正在维护中，请稍后再试。"
ReceiveSystemMaintenance = "🔧 收款功能正在维护中，请稍后再试。"
RedPacketSystemMaintenance = "🔧 红包功能正在维护中，请稍后再试。"
SwapSystemMaintenance = "🔧 闪兑功能正在维护中，请稍后再试。"
SwapMaintenanceMode = "🔧 闪兑功能正在维护中，请稍后再试。"

# 充值流程
DepositSelectCoinPrompt = "请选择您要充值的币种："
DepositSelectNetworkPrompt = "请选择 USDT 的网络类型："
DepositInfoTitleTRON = "TRON 充值地址"
DepositInfoTitleETH = "ETH 充值地址"
DepositSupportedCurrenciesTRON = "仅支持 TRC20 网络的 USDT 和 TRX。"
DepositSupportedCurrenciesETH = "仅支持 ERC20 网络的 USDT 和 ETH。"
DepositReceivingAddressTRON = "TRON (TRC20) 收款地址:"
DepositReceivingAddressETH = "ETH (ERC20) 收款地址:"
DepositQrCodeLinkText = "🖼️ 二维码"
DepositMinAmount = "最低充值金额：%s %s"
# DepositQrWarning = "请勿向此地址充值任何非指定代币，否则资产将无法找回。"
DepositCopyHint = '''
👆 点击复制钱包地址，可重复充值!
👆 上面地址和二维码不一致，请不要付款!
'''
DepositQrWarning = '''💡 提示：
- 对上述地址👆充值后, 经过3次网络确认, 充值成功!
- 请耐心等待, 充值成功后 Bot 会通知您!
'''

# 个人中心
PersonalInfo = "👤 个人信息"
USDTSymbol = "💵"
TRXSymbol = "💰"
CNYSymbol = "💴"
AccountStatement = "📝 我的账单"
WithdrawalHistory = "📊 提币历史"
SmallAmountExemption = "🔑 小额免密"
BackupAccounts = "📱 备用账号"
CNYWithdrawalHistory = "💹 CNY提现历史"
Google2FAButton = "🔐 Google二次验证"

# 备用账户管理
BackupAccountsManagement = "📱 备用账户管理"
BackupAccountsDescription = "您可以添加备用账户，以便在主账户无法使用时登录。备用账户需要通过支付密码验证才能添加。"
ManageBackupAccounts = "管理备用账户"
AddBackupAccount = "➕ 添加备用账户"
BackupAccountList = "备用账户列表"
NoBackupAccounts = "您还没有添加任何备用账户"
DeleteButton = "🗑️ 删除"
ConfirmDeleteBackupAccount = "⚠️ 确认删除此备用账户？"
BackupAccountDeletedSuccess = "✅ 备用账户已成功删除"
PleaseSetPaymentPasswordFirst = "⚠️ 请先设置支付密码"
EnterBackupAccountTelegramId = '''
💡 如何获取ID?
  - 钱包首页的账户ID
  - 转发一条消息到此机器人
  - 发送任意消息到 @nminfobot
'''
InvalidTelegramIdFormat = "❌ 无效的 Telegram ID 格式，请输入纯数字"
CannotAddSelfAsBackup = "❌ 不能添加自己作为备用账户"
VerifyingPaymentPassword = "⏳ 验证支付密码"
PleaseEnterPaymentPassword = "🔑 请输入支付密码以验证身份"
OperationTimedOut = "⏱️ 操作已超时，请重新开始"
BackupAccountAlreadyExists = "❌ 此账户已存在于系统中"
BackupAccountAddedSuccess = "✅ 备用账户添加成功"
BackupAccountVerificationTitle = "备用账户验证"
BackupAccountVerificationDesc = "%s 将您添加为备用账户\n\n请输入 %s 账户的支付密码进行验证"
RequestFrom = "请求来自"
RequestTime = "请求时间"
BackupAccountVerificationCancelled = "❌ 已经取消备用账户验证。\n\n如果需要重新验证，请返回主账户进行重新发送验证请求。"
SendVerificationMessage = "📤 发送验证消息"
BackupAccountAlreadyVerified = "✅ 该备用账户关系已经验证过了"
UnauthorizedOperation = "❌ 无权进行此操作"
PasswordLengthExceeded = "密码长度已达上限"
PasswordMustBe6Digits = "密码必须为6位数字"
PasswordIncorrect = "❌ 密码错误，请重新输入"
BackupAccountVerifiedTitle = "✅ 验证成功"
BackupAccountVerifiedDesc = "✅ 验证成功"
PaymentPasswordVerificationFailed = "❌ 支付密码验证失败"
Page = "页码"
PrevPage = "⬅️ 上一页"
NextPage = "➡️ 下一页"
CreatedAt = "创建时间"
VerificationStatus = "验证状态"
Verified = "✅ 已验证"
NotVerified = "❌ 未验证"
Confirm = "✅ 确认"
Cancel = "❌"
Google2FAManagement = "🔐 Google二次验证管理"
Google2FAEnabled = "✅ 您已启用Google二次验证。"
Google2FADisabled = "❌ 您尚未启用Google二次验证。"
Enable2FA = "🔓 启用二次验证"
Disable2FA = "🔒 禁用二次验证"
Google2FASetup = "Google二次验证设置"
Google2FAInstructions = "请使用Google Authenticator或其他支持TOTP的应用扫描二维码或手动输入以下密钥："
Google2FASecretKey = "🔑 密钥："
Google2FAVerify = "请输入验证器生成的六位数验证码："
Google2FAVerifySuccess = "✅ 验证成功，已启用Google二次验证"
Google2FAVerifyFailed = "❌ 验证失败，请重试"
Google2FADisableConfirm = "⚠️ 确认禁用Google二次验证？"
Google2FADisableSuccess = "✅ 已成功禁用Google二次验证"
Google2FAVerifyInstructions = "请输入验证码完成设置。"
VerifyCode = "✅ 验证并启用"
Google2FAAlreadyEnabled = "✅ 您已经启用了Google二次验证。"
Google2FANotEnabled = "❌ 您尚未启用Google二次验证。"
Google2FADisabledSuccess = "✅ 您已成功禁用Google二次验证。"
Google2FADisableFailed = "❌ 禁用Google二次验证失败，请重试。"
PaymentPasswordVerification = "🔑 支付密码验证"

# 支付密码
PaymentPasswordButton = "💳 支付密码"
PaymentPasswordManagement = "🔑 支付密码管理"
CurrentStatus = "状态"
PaymentPasswordEnabled = "✅ 您已设置支付密码。"
PaymentPasswordDisabled = "❌ 您尚未设置支付密码。"
SetPaymentPassword = "🔐 设置支付密码"
ConfirmSetPaymentPassword = "🔐 确认设置支付密码"
ChangePaymentPassword = "🔄 修改支付密码"
ResetPaymentPassword = "🗑️ 重置支付密码"
EnterPaymentPassword = "🔑 请输入6位数字支付密码。"
EnterCurrentPaymentPassword = "🔑 请输入当前支付密码。"
EnterNewPaymentPassword = "🔑 请输入新的6位数字支付密码。"
ConfirmPaymentPassword = "🔑 请确认您的支付密码。"
ConfirmNewPaymentPassword = "🔑 请确认您的新支付密码。"
PaymentPasswordLengthHint = "⚠️ 密码必须为6位数字。"
PaymentPasswordFormatInvalid = "❌ 支付密码格式无效，请输入6位数字。"
PaymentPasswordSetFailed = "❌ 设置支付密码失败，请重试。"
PaymentPasswordSetSuccess = "✅ 支付密码设置成功"
PaymentPasswordSetSuccessDesc = "✅ 您已成功设置支付密码，请妥善保管。"
PaymentPasswordAlreadySet = "✅ 您已设置支付密码，如需修改请使用修改功能。"
PaymentPasswordNotSet = "⚠️ 您尚未设置支付密码，请先设置支付密码。"
CurrentPaymentPasswordInvalid = "❌ 当前支付密码错误，请重试。"
PaymentPasswordChangeFailed = "❌ 修改支付密码失败，请重试。"
PaymentPasswordChangeSuccess = "✅ 支付密码修改成功"
PaymentPasswordChangeSuccessDesc = "✅ 您已成功修改支付密码，请妥善保管。"
ResetPaymentPasswordNoPermission = "❌ 您没有重置支付密码的权限，请联系客服。"
ResetPaymentPasswordSuccess = "✅ 支付密码重置成功"
ResetPaymentPasswordSuccessDesc = "✅ 您的支付密码已被重置，请重新设置支付密码。"
PaymentPasswordMaxLength = "⚠️ 密码不能超过6位数字。"
PaymentPasswordEmpty = "⚠️ 密码为空，无法删除。"
PaymentPasswordTooShort = "⚠️ 密码必须至少6位数字。"
PaymentPasswordIncorrect = "❌ 密码错误，请重试。"
PaymentPasswordMismatch = "❌ 两次输入的密码不一致，请重试。"
PaymentPasswordExactLength = "⚠️ 密码必须正好为6位数字。"               # Added for exact length requirement

# 错误提示
ErrorGrpcFailed = "❌ 服务请求失败，请稍后再试。"
ErrorInvalidSelection = "❌ 无效的选择。"
ErrorGeneric = "❌ 操作失败，请稍后再试。"
error_occurred = "❌ 发生未知错误，请稍后重试。"
InvalidAction = "❌ 无效的操作。"
UnknownAction = "❓ 未知的操作。"
DepositInfoTitleTRXTRON = "TRX 充值地址"
DepositInfoTitleETHETH = "ETH 充值地址"
DepositInfoTitleUSDTTRON = "TRC20 USDT 充值地址"
DepositInfoTitleUSDTETH = "ERC20 USDT 充值地址"
DepositDisabledForAccount = "❌ 您的账户已被禁止充值。"
DepositDisabledForToken = "❌ 该币种/网络暂停充值。"
AccountSuspended = "❌ 您的账户已被暂停，请联系客服。"

# 备用账户验证
VerifyBackupAccountTitle = "📱 验证备用账户"
EnterMainAccountPaymentPasswordPrompt = "🔑 请输入主账户的支付密码以验证此备用账户："
BackupAccountVerifiedSuccess = "✅ 备用账户验证成功！"
ErrorInvalidRequest = "❌ 错误：无效请求。"
ErrorCannotVerifyAccount = "❌ 错误：当前无法验证此账户。"
ErrorTryAgainLater = "❌ 发生错误，请稍后重试。"
ErrorInvalidState = "❌ 错误：无效的操作状态，请重新开始。"
ErrorVerificationFailed = "❌ 错误：验证失败。"

# 过期操作相关
OperationExpiredTitle = "⏱️ 操作已过期"
OperationExpiredMessage = "此操作已超时失效，请重新开始。"
TransferPasswordExpiredTitle = "⏱️ 转账密码输入已过期"
TransferPasswordExpiredMessage = "此转账密码输入已超时失效\n\n请重新发起转账操作"
ReturnToMainMenuButton = "🏠 返回主菜单"
ErrorUpdateFailedTryAgain = "❌ 验证成功，但更新状态失败。请联系支持或稍后重试。"
MainMenuButton = "🏠 主菜单"
VerificationCancelled = "❌ 验证已取消。"

# Backup Accounts Section
MasterAccount = "主账户"
BackupAccount = "备用账户"
AddedOn = "添加于"
Unknown = "未知"
Back = "◀️ 返回"
CannotDeleteMasterAccount = "❌ 主账户不能删除"
NotSet = "未设置"

# Withdraw Section
WithdrawTitle = "💸 提现"
WithdrawSelectSymbol = "请选择您要提现的币种："
WithdrawSelectChain = "请选择 %s 链："
WithdrawSelectAddressType = "请选择如何输入提现地址："
WithdrawManualAddress = "✍️ 手动输入地址"
WithdrawWhitelistAddress = "✅ 从白名单选择"
WithdrawEnterAddress = "请输入 %s(%s) 的提现地址："
WithdrawSelectAddress = "请从您的白名单中选择一个地址："
WithdrawAddNewAddress = "➕ 添加新地址"
WithdrawEnterNewAddress = "请输入要添加到白名单的新 %s (%s) 地址："
WithdrawAddressAdded = "✅ 地址已成功添加到白名单！"
WithdrawAddressInvalid = "❌ 地址格式无效。请输入有效的 %s 地址。"
WithdrawAddressExists = "❌ 该地址已在您的白名单中。"
WithdrawNoAddresses = "❌ 您的白名单中没有 %s (%s) 的地址。请添加一个新地址。"

WithdrawEnterFiatAccount = "请输入 %s 提现的收款账号："

# CNY specific
PleaseUploadQRCode = "请上传二维码图片"
WeChat = "微信"
Alipay = "支付宝"
AlipayAccount = "支付宝账号"
PaymentMethod = "收款方式"
Account = "账号"

WithdrawEnterAmount = "请输入您要提现的 %s 数量：\n\n可用余额：%s %s\n最小提现：%s %s\n最大提现：%s %s\n手续费：%s %s"
WithdrawAmountInvalid = "❌ 金额格式无效。请输入有效的数字。"
WithdrawAmountTooSmall = "❌ 金额太小。最小提现金额为 %s %s。"
WithdrawAmountTooLarge = "❌ 金额太大。最大提现金额为 %s %s。"
WithdrawInsufficientBalance = "❌ 余额不足。您的可用余额为 %s %s。"

WithdrawConfirmation = "请确认您的提现：\n\n%s\n\n确认后，您需要输入支付密码。"
WithdrawConfirm = "✅ 确认"
WithdrawCancel = "❌"

WithdrawEnterPassword = "🔑 请输入您的支付密码以完成提现："
WithdrawPasswordIncorrect = "❌ 支付密码不正确。请重试。剩余尝试次数：%s"
WithdrawPasswordLocked = "🔒 由于多次输入错误，您的支付密码已被锁定。请稍后再试。"
WithdrawNoPassword = "⚠️ 您尚未设置支付密码。请前往个人中心设置。"
WithdrawNoPasswordPrompt = "⚠️ 您尚未设置支付密码。"

WithdrawSuccess = "✅ 操作成功！您的订单已提交，请等待到账。\n\n金额：%s %s\n手续费：%s %s\n最终金额：%s %s\n地址：%s\n\n您可以在提现历史中查看状态。"
CNYWithdrawSuccess = "✅ 操作成功！您的订单已提交，请等待到账。\n\n金额：%s %s\n手续费：%s %s\n最终金额：%s %s\n\n您可以在提现历史中查看状态。"
WithdrawFailed = "❌ 提现失败：%s"
WithdrawCancelled = "❌ 提现已取消。"

WithdrawNoPermission = "❌ 您没有提现权限。请联系客服。"
WithdrawMaintenanceMode = "🚧 提现服务当前正在维护：%s"

# Withdraw Summary fields
WithdrawCurrency = "💰 币种"
WithdrawNetwork = "🌐 网络"
WithdrawAddress = "🏘️ 地址"
WithdrawRecipientName = "👤 收款人姓名"
WithdrawRecipientAccount = "🏦 收款账号"
WithdrawAmount = "💰 金额"
WithdrawFee = "💸 手续费"
WithdrawFinalAmount = "✅ 最终金额"

# Newly added keys from recent changes
WithdrawSummary = "提现信息摘要"
WithdrawSummaryHeader = "💸 提现信息摘要"
Token = "代币"
SystemError = "❌ 系统错误，请稍后重试。"
WithdrawStateExpired = "⏱️ 操作已超时或状态已过期，请重新发起提现。"
ErrorFetchingToken = "❌ 获取代币信息时出错。"
TokenNotAvailableForWithdraw = "❌ 该代币当前不可提现。"
WithdrawalTemporarilyDisabled = "🚧 该币种/网络暂时停止提现。"
ErrorProcessingRequest = "❌ 处理您的请求时出错。"
WithdrawAmountFormatInvalid = "❌ 金额格式无效，请输入有效的数字。"
AmountMustBePositive = "⚠️ 金额必须大于 0。"
FeeCalculationFailed = "❌ 计算手续费失败。"
AmountLessThanFee = "⚠️ 提现金额小于手续费。"
ErrorSendingConfirmation = "❌ 发送确认信息时出错，请稍后重试。"
Symbol = "🪙 代币"
Address = "🏘️ 地址"
FinalAmount = "✅ 到账金额"
Amount = "💰 金额"
Fee = "💸 手续费"

# Missing Keys for text_handler.go
WithdrawNoSymbolsAvailable = "❌ 暂无可提现的币种。"
NoPaymentPasswordPrompt = "⚠️ 请先设置支付密码才能使用红包功能。"
FeatureUnderDevelopment = "🚧 功能开发中，敬请期待。"
UnknownCommand = "❓ 未知命令: %s"
EchoPrefix = "回显: "

WithdrawInvalidAddressFormat = "❌ 地址格式无效 %s(%s)，请重新输入"
WithdrawAddressValidationFailed = "❌ 地址验证失败: %s，请重新输入"

# 提现成功消息
WithdrawSubmitSuccess = "✅ 提现申请已成功提交！\n\n金额：%s %s\n网络：%s\n地址：%s\n\n您的提现请求已进入处理队列，处理完成后会通知您。"

# Withdraw history section
withdrawHistoryTitle = "📊 取款历史"
withdrawQRCode = "提现二维码"
withdrawQRCodeAvailable = "已上传"
withdrawRecordID = "#️⃣ 订单"
withdrawOrderNumber = "📋 订单号"
withdrawDeductionAmount = "💸 扣除金额"
withdrawReceivedAmount = "💰 到账金额"
withdrawTime = "⏰ 时间"
withdrawRemarks = "📝 备注"
withdrawAmount = "💰 金额"
withdrawStatus = "🚦 状态"
withdrawNetwork = "🌐 网络/链"
withdrawAddress = "🏘️ 地址"
withdrawTxHash = "🔗 交易哈希"
withdrawReason = "⛔ 拒绝原因"
withdrawCreatedAt = "⏰ 时间"
withdrawStatusPending = "⏳ 待处理"
withdrawStatusProcessing = "⚙️ 处理中"
withdrawStatusSuccess = "✅ 成功"
withdrawStatusFailed = "❌ 失败"
withdrawStatusRejected = "⛔ 已拒绝"
withdrawStatusUnknown = "❓ 未知"
withdrawFiatType = "💳 提现方式"
withdrawFiatTypeAlipayAccount = "支付宝账号"
withdrawFiatTypeAlipayQR = "支付宝二维码"
withdrawFiatTypeWechatQR = "微信二维码"
buttonPreviousPage = "⬅️ 上一页"
buttonNextPage = "下一页 ➡️"
buttonBack = "◀️ 返回"
buttonMainMenu = "🏠 主菜单"
noWithdrawRecords = "暂无提币记录"
errorParsingCallback = "❌ 解析回调数据错误"
errorLoadingWithdrawHistory = "❌ 加载提币记录错误"
errorFormattingWithdrawRecord = "❌ 格式化提币记录错误"
errorBuildingKeyboard = "❌ 创建键盘错误"
userNotFound = "❌ 用户不存在"
WithdrawAddressAddFailed = "❌ 添加地址失败: %s，请重新输入"
WithdrawAmountBelowMinimum = "❌ 提现金额低于最小限额 (%s %s)。"
transferAmountBelowMinimum = "❌ 转账金额低于最小限额 (%s %s)。"
transferAmountExceedsMaximum = "❌ 转账金额超过最大限额 (%s %s)。"
transferInsufficientBalance = "❌ 余额不足。您的可用余额为 %s %s。"
WhitelistAddress="🏘️ 白名单地址"

# 分页相关键
WithdrawSelectAddressPaged = "请从您的白名单中选择一个地址 (第 %d/%d 页)："
WithdrawStateExpiredOrInvalid = "⏱️ 操作已超时或状态无效，请重新发起提现。"


# 转账功能
inlineTransferInstruction = "请在您想发起转账的聊天窗口（与好友或在群组中）输入 @{BotUsername} {Keyword} 并选择结果。"
promptTransferAmountGeneric = "请输入转账金额 (USDT)。如果是群组转账，请同时 @提及收款人 (例如: 100 @username)。"
inlineResultTransferTitle = "⬆️ 发起一笔转账"
inlineResultTransferDesc = "点击此处输入转账金额"
errorInlineQueryFailedTitle = "❌ 错误"
errorInlineQueryFailedDesc = "❌ 无法处理您的请求，请稍后再试。"
errorInvalidAmountFormat = "❌ 无效的金额格式。"
errorCannotDetermineReceiverPrivate = "❌ 无法确定私聊收款人。"
errorParsingAmountMention = "❌ 无法解析金额或收款人，请按格式输入 (例如: {Example})。"
errorRecipientNotSpecifiedGroup = "❌ 请在群组中 @提及 收款人。"
errorReceiverNotFound = "❌ 找不到指定的收款人 %s。"
errorTransferToSelf = "❌ 不能给自己转账。"
errorUnsupportedChatType = "❌ 不支持在此聊天类型中发起转账。"
errorTransferInitiationFailed = "❌ 发起转账失败，请稍后重试。"
errorInsufficientBalance = "❌ 账户余额不足。"
transferInitiatedInlineSuccess = "✅ 已向 %s 发起转账请求，请等待对方收款。"
errorCannotSendToReceiver = "❌ 无法向收款人发送收款消息，请联系客服处理。"
errorCannotFindReceiverChatID = "❌ 无法找到收款人的聊天信息，请联系客服处理。"
transferReceived = "✅ 已收款"
transferExpired = "⏱️ 已过期"
transferAlertSender = "⏳ 您是转账发起人，请等待收款人操作。"
transferAlertUnauthorized = "⛔ 您无权操作此转账。"
transferSuccessSenderNotify = "✅ 您向 %s 转账 %s %s 已成功。"
transferSuccessReceiverNotify = "✅ %s 已收到来自 %s 的 %s %s。"
transferExpiredSenderNotify = "⏱️ 您向 %s 转账 %s %s 的请求已过期。"
transferExpiredReceiverNotify = "⏱️ 来自 %s 的 %s %s 转账请求已过期。"
transferError = "❌ 转账处理失败：%s"
errorInvalidCallbackData = "❌ 无效的回调数据。"
errorTransferNotFound = "❌ 未找到转账记录。"

alertCollectRaceConditionOrInvalid = "⚠️ 此转账已被领取或状态已变更"


# 转账 - 选择收款人
transferRecipientSelectionPrompt = '''💸 请使用以下方法选择一个收款人：

- 发送收款人的 @username
- 转发一条收款人的消息到这里
- 发送收款人的【钱包页面】->【账户ID】'''
transferSelectRecipientButton = "👥 选择收款人"


transferSelectTokenPrompt="请选择您要转账的代币："

# 内联查询 - 转账确认
# transferSearchResultDesc = "转账 %s %s | 您的余额: %s %s" # Removed, replaced by combined key below
invalidAmountFormat = "❌ 无效的金额格式" # 保留
ErrorInvalidQueryFormat = "❌ 无效的查询格式" # 保留
Error = "❌ 错误" # 保留
transferConfirmInlineTitle = "✅ 确认转账" # 保留
# transferConfirmDescSimple = "转账 %s %s" # Removed, replaced by combined key below
transferConfirmDescWithBalanceAndWarning = "❗转账 %s %s | 余额: %s %s\n❗您正在向对方转账,并且立刻生效。" # New combined description with balance and warning for preview
# transferConfirmDescWithWarning = "❗转账 %s %s\n❗ 您正在向对方转账 %s 并且立刻生效。" # Keep for message on click
# 移除了 transferConfirmMessage, userNotFoundOrInvalid, invalidRecipientID
# insufficientBalanceTitle = "余额不足" # Removed, replaced by formatted version below
insufficientBalanceTitleWithDetails = "❌ %s 余额不足 (当前: %s)" # New formatted title
# insufficientBalanceDesc = "您的 %s 余额不足 (%s)" # Removed, replaced by detailed version below
insufficientBalanceDescDetailed = "❌ 转账 %s %s 失败。所需金额 %s %s，可用余额 %s %s。" # New detailed description
errorInvalidTokenSymbol = "❌ 无效的代币符号： %s" # New key for invalid token symbol
OpenBot = "🤖 打开机器人"

transferNeedsVerification = "💰 转账给你 %s %s\n\n等待付款方验证支付密码"
verifyPaymentPasswordButton = "🔑 验证支付密码"
transferConfirmDescNoWarning = "💰 转账给你 %s %s"

# 小额免密设置
PassFree_SettingsTitle = "🔑 小额免密设置"
PassFree_CurrentLimitFormat = "当前额度: %s %s"
PassFree_NotSet = "❌ 未设置"
PassFree_SetLimitButtonFormat = "设置 %s 额度"
PassFree_ErrorFetchingTokens = "❌ 获取支持的代币列表失败。"
PassFree_NoTokensAvailable = "❌ 没有可用于小额免密设置的代币。"
PassFree_ErrorFetchingSettings = "❌ 获取当前小额免密设置失败。"
PassFree_ErrorInvalidCallback = "❌ 无效的操作或回调数据。"
Error_FailedToSetUserState = "❌ 更新操作状态失败，请重试。"
PassFree_EnterAmountPromptFormat = "请输入 %s 的新免密金额 (当前: %s):\n(输入 0 可禁用)"
Error_Internal = "❌ 发生内部错误，请稍后重试。"
PassFree_InvalidAmountFormat = "❌ 金额格式无效，请输入有效的数字。"
PassFree_AmountCannotBeNegative = "❌ 金额不能为负数。"
PassFree_ErrorSettingLimit = "❌ 设置小额免密金额失败，请重试。"
PassFree_SetSuccessFormat = "✅ %s 的小额免密金额已成功设置为 %s。"

# Transfer Module Specific Keys (Generated from Plan)
"promptEnterPassword" = "🔑 请输入支付密码"
"transferStatusPendingPass" = "⏳ 待付款方验证"
"transferMessagePendingPass" = "%s 向 %s 发起了一笔 %s 的转账，等待付款方验证密码。"
"transferStatusPendingCollection" = "⏳ 待收款"
"transferMessagePendingCollection" = "您有来自 %s 发起了一笔 %s %s 的转账，等待领取。"
"transferStatusCompleted" = "✅ 已领取"
"transferMessageCompleted" = "{sender} 向 %s 发起的 %s 转账已被领取。"
"transferStatusExpired" = "⏱️ 已过期"
"transferMessageExpired" = "%s 向 %s 发起的 %s 转账已过期。"
"transferStatusFailed" = "❌ 处理失败"
"transferMessageFailed" = "%s 向 %s 发起的 %s 转账处理失败。"
"transferStatusInsufficientFunds" = "❌ 余额不足"
"transferMessageInsufficientFunds" = "%s 向 %s 发起的 %s 转账因余额不足失败。"
"transferMessageUnknownStatus" = "❓ 转账状态异常: %s"
"transferMemoLabel" = "📝 备注: %s"
"transferReceivedNotify" = "💰 收到来自 %s 的转账：%s %s"
"transferReceivedNotifyDetailed" = "收到付款%s%s\n来自: %s\n订单号: %s"
"transferConfirmPrompt" = "📤 确认转账信息：\n\n收款人：%s (ID: %s)\n金额：%s %s\n\n⚠️ %s\n\n请确认以上信息无误。"
"transferConfirmButton" = "✅ 确认转账"
"transferCancelButton" = "❌ 取消"
"alertCollectNotReceiver" = "⛔ 您不是此转账的指定收款人"
"alertCollectIsSender" = "⛔ 您不能领取自己发起的转账"
"alertCollectAlreadyCompleted" = "⚠️ 此转账已被领取"
"alertCollectExpired" = "⏱️ 此转账已过期"
"alertCollectFailedGeneric" = "❌ 转账处理失败"
"alertCollectInsufficientFunds" = "❌ 发送者余额不足"
"transferSenderInsufficientBalance" = "❌ 无法领取转账：发送者余额不足"
"alertCollectPendingPass" = "⏳ 此转账等待付款方验证"
"alertCollectSuccess" = "✅ 收款成功！"
"alertPasswordLocked" = "🔒 支付密码已锁定，请稍后再试"
"alertPasswordIncorrect" = "❌ 支付密码错误"
"alertPasswordNotSet" = "⚠️ 用户未设置支付密码"
"alertPasswordVerificationFailed" = "❌ 密码验证失败"
"alertPasswordVerified" = "✅ 密码验证成功！"
"transferPasswordVerificationCompleted" = "✅ 操作完成"
"transferMessageInitiatedPendingPass" = "%s 发起了一笔 %s %s 的转账，请验证支付密码。"
"transferMessageInitiatedPendingCollection" = "您有来自 %s 发起了一笔 %s %s 的转账，等待领取。"
"transferNotAvailable" = "🚧 转账功能当前不可用。"

"transferConfirmDescWithBalance"="⬆️ 转账 %s %s | 余额: %s %s"

# Shared Keys (Generated from Plan)
SharedErrorPasswordVerifierServiceUnavailable = "❌ 验证服务暂时不可用"
SharedErrorPasswordVerifierAccountLocked = "🔒 账户已锁定，请稍后再试"
SharedErrorPasswordVerifierAccountLockedWithMinutes = "🔒 账户已锁定，请 %d 分钟后再试"
SharedErrorPasswordVerifierGetUserInfoFailed = "❌ 获取用户信息失败"
SharedErrorPasswordVerifierUserNotFound = "❌ 用户不存在"
SharedErrorPasswordVerifierPasswordNotSet = "⚠️ 未设置支付密码"
SharedErrorPasswordVerifierTooManyAttempts = "🔒 密码错误次数过多，账户已锁定 %d 小时"
SharedErrorPasswordVerifierIncorrectWithAttempts = "❌ 密码错误，还有 %d 次尝试机会"
SharedErrorPasswordVerifierSetPermissionFailed = "❌ 设置操作许可失败"

"collectTransferButton"="👑 领取"
"EnterTransferPaymentPasswordPrompt"="🔑 请输入支付密码以完成转账："
"OperationCancelled"="❌ 操作已取消"
"TransferReadyToCollect"="⏳ 待领取"

"failed_insufficient_funds"="❌ 余额不足"

# 转账 - 收款人输入处理
transferInvalidRecipientInputFormat = "❌ 无效的收款人信息格式。请发送 @username、转发消息或账户ID。"
transferInvalidRecipientInputType = "❌ 收到的消息类型无效，请发送文本或转发消息。"
transferRecipientNotFound = "❌ 未找到您指定的收款人，请检查输入或换一种方式。"
transferRecipientFindError = "❌ 查找收款人时出错，请稍后再试。"

# 转账 - 金额与确认流程
transferPromptForAmountWithBalance = "已选择收款人: %s (ID: %s)。\n您的 %s 余额: %s\n请输入转账金额:"
transferInvalidAmountFormat = "❌ 无效的金额格式，请输入有效的数字。"
transferAmountTooLow = "❌ 转账金额必须大于 0。"
transferConfirmationPrompt = "请确认转账:\n收款人: %s (ID: %d)\n金额: %s %s"
transferPromptForPassword = "您正在进行转账\n金额: %s %s"
transferIrreversibleWarning = "提示：本次转账即时完成，无法追回！"
transferCancelled = "❌ 转账已取消。"
transferSuccessNotify = "✅ 转账成功！\n您已向 %s (ID: %d) 转账 %s %s。"
transferSuccessNotifyDetailed = "成功转账给%s\n用户ID: %d\n名称: %s\n用户名: %s\n支付金额: %s%s\n订单号: %s\n提示: 您可以将此支付凭证转发给收款人。"
transferReceivedNotification = "💰 您收到了一笔转账！\n\n来自：%s (%s)\n金额：%s %s\n订单号：%s\n\n转账已到账，请查看余额。"
transferFailedNotify = "❌ 转账失败: %s"
transferRecipientConfirmed = "✅ 已选择收款人: %s (ID: %d)。"


# Transfer Cancellation
inlineTransferCancelledButton = "❌ 转账已取消"
EnterPasswordForTransferPrompt="🔑 请输入支付密码以授权转账 %s %s"
# 转账记录功能
TransferHistoryTitle = "📊 转账记录"
TransferHistoryAllTokens = "全部代币"
TransferHistoryPageInfo = "第 %d 页 / 共 %d页"
TransferHistoryPreviousPage = "⬅️ 上一页"
TransferHistoryNextPage = "下一页 ➡️"
TransferHistoryBackButton = "◀️ 返回"
TransferStatusPending = "⏳ 处理中" # 复用现有? 检查是否需要特定翻译
TransferStatusCompleted = "✅ 已完成" # 复用现有? 检查是否需要特定翻译
TransferStatusExpired = "⏱️ 已过期" # 复用现有? 检查是否需要特定翻译
TransferStatusFailed = "❌ 失败" # 补充完整性，复用现有?
TransferDirectionIn = "📥 收款"
TransferDirectionOut = "📤 付款"
TransferPeerUnknown = "👤 未知用户"
TransferHistoryNoRecords = "暂无转账记录"
# 转账记录 - 优化显示
TransferHistoryTypeLabel = "类型"
TransferHistoryRecipientLabel = "👥 收款人"
TransferHistorySenderLabel = "👤 付款人"
TransferHistoryTokenLabel = "🪙 代币"
TransferHistoryAmountLabel = "💰 金额"
TransferHistoryStatusLabel = "🚦 状态"
TransferHistoryTimeLabel = "⏰ 时间"
TransferHistoryCurrentUserLabel = "👤 我"

# 红包封面相关
PreviousPage = "⬅️ 上一页"
Delete = "🗑️ 删除"
UploadImage = "🖼️ 上传图片"
Status = "🚦 状态"
StatusApproved = "✅ 已通过"
StatusPendingReview = "⏳ 审核中"
StatusRejected = "⛔ 已拒绝"
RejectReasonPrefix = "⛔ 拒绝原因:"
NoRedPacketCovers = "❌ 你还没有上传红包封面图片。请点击「上传图片」按钮上传。"
ConfirmDeleteCover = "⚠️ 确定要删除这个红包封面吗？此操作不可撤销。"
ConfirmDelete = "✅ 确认删除"
CoverDeleteSuccess = "✅ 封面已成功删除"
UserNotFound = "❌ 找不到用户信息"
FailedToLoadCovers = "❌ 加载封面失败，请重试"
InvalidPageNumber = "❌ 无效的页码"
InvalidImageID = "❌ 无效的图片ID"
SetStateFailed = "❌ 设置状态失败，请重试"
UploadCoverPrompt = "🖼️ 请发送你想用作红包封面的图片"
PleaseUploadImage = "🖼️ 请上传一张图片"
ImageValidationError = "❌ 图片验证失败，请重试"
ImageInvalid = "❌ 图片无效"
UploadFailed = "❌ 上传失败，请重试"
SaveImageFailed = "❌ 保存图片信息失败，请重试"
CoverUploadSuccess = "✅ 封面上传成功！你的封面正在审核中，审核通过后才能使用。"

# Red Packet Menu
RedPacketMenuTitle = "🧧 红包菜单"
RedPacketOngoingButton = "🎯 进行中"
RedPacketEndedButton = "✅ 已结束"
RedPacketAllButton = "📋 全部"
RedPacketAddButton = "➕ 添加"
RedPacketSetCoverButton = "🖼️ 封面管理"

UploadRedPacketCoverPrompt = "🖼️ 请发送你想用作红包封面的图片 (%s %s %s %s)"

RedPacketCoverUploadedPendingReview = "✅ 封面已成功上传，正在审核中。审核通过后即可使用。"
# 红包封面状态
rp_cover_status_pending_review = "⏳ 审核中"
rp_cover_status_success = "✅ 已通过"
rp_cover_status_fail = "❌ 未通过"

ConfirmDeleteRedPacketCover="⚠️ 确定要删除这个红包封面吗？此操作不可撤销。"
# Red Packet Flow
FailedToLoadTokens = "❌ 加载代币列表失败"
SelectRedPacketTokenTitle = "请选择红包代币"
InvalidSelection = "❌ 无效选择"
SelectRedPacketTypeTitle = "请选择红包类型"
SelectedRandomTypeForToken = "已选择随机红包 (代币ID: %d)" # %d is placeholder for token ID
SelectedFixedTypeForToken = "已选择固定金额红包 (代币ID: %d)" # %d is placeholder for token ID
RedPacketTypeRandomButton = "🎲 随机金额"
RedPacketTypeFixedButton = "💰 固定金额"
# Red Packet Creation Flow
EnterRedPacketQuantity = "请输入红包数量（%d-%d个）："
InvalidRedPacketQuantity = "❌ 输入无效，请输入一个%d-%d之间的整数。"
RedPacketQuantityExceedsLimit = "❌ 红包数量不能超过%d个，请重新输入。"
RedPacketQuantityBelowMinimum = "❌ 红包数量不能少于%d个，请重新输入。"
RedPacketQuantitySet = "✅ 红包数量已设置为: %d"
TooManyAttempts = "❌ 尝试次数过多，请重新发起红包创建。"
SetUserStateError = "❌ 系统繁忙，设置用户状态失败，请稍后再试。"
# Red Packet Amount Input
EnterRedPacketTotalAmount = "请回复你要发送的总金额(%s)？例如：8.88"
EnterRedPacketSingleAmount = "请回复你要发送的单个金额(%s)？例如：8.88"
InvalidAmountFormat = "❌ 金额格式无效，请输入有效的数字。"
AmountPrecisionError = "⚠️ 金额小数位数不能超过 3 位。"
RedPacketMinAmountError = "⚠️ 单个红包金额不能低于 %s。"
InsufficientBalance = "❌ 账户余额不足。"
GetBalanceError = "❌ 获取账户余额失败，请稍后再试。"
ConvertBalanceError = "❌ 转换账户余额失败，请稍后再试。"
RedPacketAmountSet = "✅ 红包金额已设置。"
# Red Packet Blessing Flow
EnterRedPacketBlessingPrompt = "请为您的红包添加一条留言:"
InputBlessingButton = "✍️ 输入留言"
SkipBlessingButton = "➡️ 使用默认"
EnterBlessingContentPrompt = "请输入留言内容 (最多 200 个字符):"
BlessingTooLong = "❌ 输入内容过长，请限制在 200 字符以内。请重新输入:"
BlessingPreview = "当前留言:\n%s"
ConfirmButton = "✅ 确认" # Note: This might be a general key, ensure it doesn't conflict
ReEnterButton = "🔄 重新输入" # Note: This might be a general key, ensure it doesn't conflict
BlessingSkipped = "✅ 已跳过留言，准备选择封面..." # Temporary
BlessingConfirmed = "✅ 留言已确认，准备选择封面..." # Temporary
# 红包封面选择流程
ConfirmCoverTitle = "🖼️ 确认红包封面"
RedPacketConfirmCoverButton = "✅ 确认"
CustomCoverButton = "🖼️ 自定义封面"
CustomCoverSelectionTitle = "🖼️ 选择自定义封面"
NoCustomCovers = "❌ 暂无已通过审核的自定义封面"
UseThisCoverButton = "✅ 使用此封面"
PageIndicator = "%d/%d"
CoverConfirmed = "✅ 封面已确认，正在创建红包..."
NotImplementedYet = "🚧 功能暂未实现" # 通用占位符
# Red Packet Summary & Confirmation (New Keys)
RedPacketSummaryTitle = "🎁 红包确认"
RedPacketTypeLabel = "🎁 类型"
RedPacketTypeFixed = "💰 普通红包" # Text representation
RedPacketTypeRandom = "🎲 随机红包" # Text representation
TypeLabel = "类型"
TokenLabel = "代币"
RedPacketQuantityLabel = "🔢 数量"
RedPacketBlessingLabel = "📝 备注"
DefaultBlessing = "🎉 恭喜发财，大吉大利！"
FixedAmountLabel = "💰 单个金额"
TotalAmountLabel = "💰 总金额"
RedPacketCancelled = "❌ 红包已取消。"
RedPacketCreationFailed = "❌ 红包创建失败：%s"
RedPacketCreatedSuccess = "✅ 红包创建成功！"
RedPacketPremiumStatus = "会员红包"
RedPacketNormalStatus = "普通红包"
SetAsPremiumRedPacket = "🌟 设为会员红包"
CancelPremiumRedPacket = "❌会员红包"
RedPacketPremiumUpdated = "✅ 红包会员状态已更新"
RedPacketPremiumUpdateFailed = "❌ 更新红包会员状态失败"
RedPacketSetPremiumSuccess = "✅ 成功设置为会员红包"
RedPacketCancelPremiumSuccess = "✅ 成功取消会员红包"
RedPacketPremiumRequired = "❌ 抱歉，此红包仅限 Telegram Premium 会员领取"
RedPacketNotFound = "❌ 红包不存在"
InvalidOperation = "❌ 无效操作"
InvalidOperationOrExpired = "⏱️ 操作无效或已过期"
SystemErrorConfigMissing = "❌ 系统错误：配置缺失"
UnknownOperation = "❓ 未知操作"
# Red Packet Password Verification
RedPacketEnterPasswordPrompt = "🔑 请输入支付密码以创建红包:\n🔑"
RedPacketCreationPending = "⏳ 红包准备创建中..." # 用于免密或验证成功后的 TODO 占位消息
RedPacketPasswordFailedGeneric = "❌ 支付密码验证失败，请重试。" # 通用验证失败提示
RedPacketPasswordCancelled = "❌ 红包创建已取消。" # 取消密码输入
# 红包分享
ShareRedPacketButton = "📤 分享红包"
ErrorInvalidShareQuery = "⚠️ 无效的分享请求。"
ErrorRedPacketNotFoundOrQueryFailed = "❌ 红包信息查询失败或红包不存在。"
ErrorRedPacketNotFound = "⚠️ 未找到该红包。"
ErrorTokenInfoQueryFailed = "⚠️ 代币信息查询失败。"
ShareRedPacketInlineTitle = "来自 %s 的红包"
ShareRedPacketInlineDescription = "%d个%s红包 - %s"
ShareRedPacketSentMessage = "🧧 %s 发送了一个红包\n💵 总金额:%s %s\n💰 剩余金额:%s\n📦 剩余数量:%d"
ShareRedPacketSentMessageMemo = "📝 留言: %s"
ShareRedPacketPremiumOnly = "⭐ 仅限 Telegram Premium 会员领取"
"RedPacketTypeRandomDisplay" = "🎲 随机"
"RedPacketTypeFixedDisplay" = "💰 固定"
# 红包内联查询 V2
"RedPacketStatusLabel" = "🚦 状态"
"RedPacketRemainingQuantityLabel" = "🔢 剩余数量"
"RedPacketRemainingAmountLabel" = "💰 剩余金额"
"RedPacketStatusActive" = "✅ 进行中"
"RedPacketStatusEmpty" = "🎉 已领完"
"RedPacketStatusExpired" = "⏱️ 已过期"
"RedPacketStatusCancelled" = "❌ 已取消" # 根据 entity 定义添加
"RedPacketStatusUnknown" = "❓ 未知 (%s)" # 未知状态字符串占位符
"ShareRedPacketInlineDescriptionDetailed" = "剩余: %d | 金额: %s %s | 状态: %s"
"ShareRedPacketInlineTitleDetailed" = "来自 %s 的 %s 红包"
# Cover Logic Translations
"ErrorGenericFallback" = "❌ 处理时发生错误，请稍后再试。"
"ErrorInternalRetry" = "❌ 内部错误，请稍后重试"
"ErrorGetStateFailedRetry" = "❌ 获取操作状态失败，请重试"
"ErrorStateExpiredOrInvalid" = "⏱️ 操作已过期或无效"

# 红包领取通知
RedPacketClaimNotificationTitle = "🎉 红包被领取"
RedPacketClaimNotificationClaimer = "👤 领取人: %s"
RedPacketClaimNotificationAmount = "💰 金额: %s %s"
RedPacketClaimNotificationRedPacketID = "🧧 红包ID: %s"
RedPacketClaimNotificationTime = "⏰ 时间: %s"
RedPacketClaimNotificationMessage = "🎉 <b>红包被领取</b>\n\n👤 领取人: %s\n💰 金额: %s %s\n🧧 红包ID: <code>%s</code>\n⏰ 时间: %s"
"ErrorGetUserInfoFailedRetry" = "❌ 获取用户信息失败，请稍后重试"
"ErrorSetStateFailedRetry" = "❌ 设置操作状态失败，请重试"
"ErrorGetCoverInfoFailedRetry" = "❌ 无法获取封面信息，请重试"
"ErrorDeleteCoverFailed" = "❌ 删除封面失败"
"ErrorDeleteCoverFailedRetry" = "❌ 删除封面失败，请稍后重试"
"ErrorGetCoverImagesFailed" = "❌ 获取封面图片失败"
"ErrorGetCoverListFailedRetry" = "❌ 获取封面列表失败，请稍后重试"
"NoCoversUploadedYet" = "❌ 您还没有上传过红包封面"
"ErrorLoadCoverInfoFailedRetry" = "❌ 无法加载封面信息，请稍后重试"
"StatusSuccess" = "🚦 状态: 成功"
"ReasonNotProvided" = "❌ 未提供"
"StatusFailWithReason" = "🚦 状态: 审核失败\n⛔ 原因: %s"
"StatusUnknownWithValue" = "🚦 状态: 未知 %s"
"CoverPageInfo" = "%s \n🖼️ 封面 %d/%d"
"CoverImageRequirements" = "🖼️ 图片要求:\n- 📏 尺寸: 200×200 至 1000×1000像素\n- 🖼️ 格式: JPG 或 PNG\n- 💾 大小: 不超过2MB\n- ⚠️ 内容: 请勿上传违规内容，所有图片需经过审核"
"ButtonUploadCover" = "🖼️ 添加"
"ButtonBack" = "◀️ 返回"
"ButtonPrevious" = "<< "
"ButtonDelete" = "🗑️ 删除"
"ButtonNext" = ">> "
"ButtonConfirmDelete" = "✅ 确认删除"
"ButtonCancel" = "❌"
"ButtonSelectCover" = "✅ 选择此封面"
"NoApprovedCoversAvailable" = "❌ 没有可用的封面图片，请先上传并等待审核通过。"
"SelectCoverPageInfo" = "🖼️ 选择封面 %d/%d"
"CoverSelectedSuccessfully" = "✅ 封面已选择，返回支付确认页面..."
"ErrorRedPacketRecordNotFound" = "❌ 红包记录未找到，请重新创建。"
"ErrorUpdateCoverFailed" = "❌ 更新封面失败，请重试。"

# Ensure existing keys have correct values (add/update if necessary)
# Note: If these keys already exist with different values, this might overwrite them.
# Consider reading the file first if precise checking is needed.
RedpacketCoverUploadSuccess = "✅ 封面上传成功，正在等待审核。"
RedpacketCoverDeleteConfirmPrompt = "⚠️ 您确定要删除此封面吗？"
RedpacketCoverDeleteSuccessPartialRefresh = "✅ 删除成功，但刷新页面可能不准确"
RedpacketCoverUploadPrompt = "🖼️ 请发送一张图片作为红包封面。"
error_cover_dimensions = "❌ 图片尺寸不符合要求，请确保图片宽高均在 200 到 1000 像素之间。"

ClaimRedPacketButton="🎁 领取红包"
RedPacketEmptyButton="❌ 红包已领完"
RedPacketCancelledButton="❌ 红包已取消"
RedPacketExpiredButton="⏱️ 红包已过期"
RedPacketFullyClaimed="红包已经被领取完了！"
RedPacketCancelledMessage="红包已经被取消了！"
RedPacketExpiredMessage="红包已经过期了！"

# 红包领取异步流程
RedPacketClaimSubmitted = "⏳ 您的红包领取请求正在处理中..."
RedPacketClaimSuccessWithAmount = "🎉 恭喜！您领到了 %s %s 红包！"
RedPacketNotActive = "❌ 红包尚未激活。"
RedPacketAlreadyClaimed = "❌ 您已经领取过这个红包了。"
RedPacketEmpty = "🎉 红包已经被领完了。"
RedPacketClaimFailedSystem = "❌ 系统繁忙，领取红包失败，请稍后再试。"
RedPacketClaimRedisLockFailed = "❌ 系统繁忙，请稍后再试（获取锁失败）。"
KafkaPublishFailed = "❌ 发送领取请求失败，请稍后再试。"
KafkaConsumeError = "❌ 处理领取请求时发生错误。"
NotificationSendFailed = "❌ 发送领取结果通知失败。"

# Red Packet History
ButtonViewClaims = "📜 查看领取记录"
ButtonBackToMenu = "🏠 返回菜单"
ButtonBackToSent = "⬅️ 返回红包详情"
SentHistoryPageTitle = "🎁 发出的红包"
NoSentRedPackets = "❌ 您还没有发出过红包。"
NoActiveRedPackets = "❌ 您没有进行中的红包。"
NoEndedRedPackets = "❌ 您没有已结束的红包。"
SentInfoFormat = "🎁 类型: %s\n💰 金额: %s %s\n💰 余额: %s %s\n🚦 状态: %s\n⏰ 时间: %s\n📊 进度: %d/%d"
ClaimHistoryPageTitle = "📜 红包 #%d 领取记录"
NoClaimsYet = "❌ 还没有人领取这个红包。"
ClaimInfoFormat = "%d. %s 领取了 %s %s (%s) \n"

# 红包记录功能

# 红包撤销功能
ButtonCancelRedPacket = "🚫 撤销红包"
ConfirmCancelRedPacket = "⚠️ 确定要撤销这个红包吗？撤销后未领取的金额将退还。"
ButtonConfirmCancel = "✅ 确认撤销"
ButtonCancelAction = "❌"
RedPacketCancelledSuccess = "✅ 红包已成功撤销，金额已退还。"
RedPacketCancelledFailed = "❌ 红包撤销失败，原因：%s"
RedPacketCannotCancelNotActive = "❌ 只有进行中的红包才能撤销。"
RedPacketCannotCancelClaimed = "❌ 红包已被领取，无法撤销。"
RedPacketCannotCancelFullyClaimed = "❌ 红包已被全部领取，无法撤销。"
RedPacketCannotCancelNotCreator = "❌ 您不是该红包的创建者，无法撤销。"
DatabaseError = "❌ 数据库操作失败，请稍后重试。"

# 收款流程
ReceiveNoTokensMessage = "❌ 当前没有可用于收款的代币。"

# 收款功能 (Receive Payment)
ReceivePromptSelectToken = "请选择您要收款的币种:"
ReceiveButtonGetShareableLink = "🔗 获取分享链接 (%s)"
ReceiveAlertShareLinkGenerated = "✅ 已生成 %s 收款链接：\n{Link}\n\n请复制并分享给付款方。"
ReceiveInlineResultTitle = "⬇️ 收款请求: %s %s"
ReceiveInlineResultDescription = "来自 %s 的收款请求"
ReceiveInlineResultMessageText = "%s 向您发起一笔收款请求\n💰 金额: %s %s\n请点击下方按钮进行支付。"
ReceiveButtonPayNow = "立即支付"
ReceiveErrorCreateRequestFailed = "❌ 创建收款请求失败"
ReceiveErrorFetchRequestFailed = "❌ 获取收款请求失败"
ReceiveErrorPaymentFailed = "❌ 支付失败"
ReceiveErrorAlreadyPaid = "❌ 收款请求已支付"
ReceiveErrorExpired = "⏱️ 收款请求已过期"
ReceiveErrorCancelled = "❌ 收款请求已取消"
ReceiveErrorNotFound = "❌ 收款请求不存在"
ReceiveErrorCannotPaySelf = "❌ 不能向自己支付"


# Receive Payer Selection
receiveSelectPayerButton = "👥 选择付款人"
receivePayerSelectionPrompt = "💸 您已选择使用 %s 收款。\n -点击下方选择付款人按钮进行收款"



ReceiveErrorInvalidFormat = "❌ 无效的格式"
ReceiveErrorInvalidAmount = "❌ 无效的金额"
ReceiveErrorTokenNotAllowed = "❌ 代币不允许收款"
ReceiveErrorAmountOutOfRange = "❌ 金额超出范围"
ReceivePromptEnterPasswordWithDetails = "🔑 请输入支付密码以完成付款:\n%s\n%s"
ReceiveLabelRequester= "-请求者"
ReceiveLabelAmount = "-💰 金额"
ReceiveLabelMemo = "-📝 留言"
ReceiveLabelStatus = "-🚦 状态"
ReceiveLabelCreatedAt = "-⏰ 创建时间"
ReceiveLabelExpiresAt = "-⏰ 过期时间"
ReceiveLabelPaidAt = "-⏰ 支付时间"
ReceiveLabelCancelledAt = "-⏰ 取消时间"
ReceiveLabelUpdatedAt = "-⏰ 更新时间"
ReceiveLabelDeletedAt = "-⏰ 删除时间"
ReceiveLabelSymbol = "-🪙 代币符号"
ReceiveLabelStatusPending = "-⏳ 等待付款"
ReceivePromptConfirmPaymentPassFree = "请确认以下收款信息 (免密支付):\n\n%s\n\n点击下方按钮完成支付。"
ReceivePromptConfirmPayment = "请确认以下收款信息:\n\n%s\n\n🔑 请输入支付密码以完成支付。"
ReceivePromptConfirmPaymentWithMemo = "请确认以下收款信息:\n\n%s\n\n📝 留言: %s\n\n🔑 请输入支付密码以完成支付。"
ButtonConfirmPayment="✅ 确认支付"
ReceiveSuccessPaymentComplete = "✅ 支付成功！"
ReceiveSuccessPaymentCompleteWithMemo = "✅ 支付成功！\n📝 留言: %s"
ReceiveErrorPaymentFailedWithReason = "❌ 支付失败: %s"
ReceiveErrorPaymentFailedWithReasonAndMemo = "❌ 支付失败: %s\n📝 留言: %s"
ReceiveErrorPaymentFailedWithMemo = "❌ 支付失败\n📝 留言: %s"
Google2FAEnterCode = "🔑 请输入Google验证码"
Google2FACodeInvalidRetry = "❌ 验证码无效，请重试"
Google2FACodeInvalid = "❌ 验证码无效"
Google2FACodeFormatError = "❌ 验证码格式错误"

# Transfer Verification Errors
ErrorNotTransferSender = "此操作只能由付款方执行。"
TransactionType_red_packet_claim = "领取红包"
TransactionType_red_packet_refund = "红包退款"
TransactionType_red_packet_create="创建红包"

ErrorNeedSetPaymentPasswordTitle = "请设置支付密码"
ErrorNeedSetPaymentPasswordDesc = "为了您的资金安全，请先设置支付密码后再进行操作。"
ErrorCheckingSecurityStatus = "检查账户状态时出错，请稍后重试。"
InvalidTelegramIdFormat10Digits="❌ 无效的 Telegram ID 格式，请输入 10 位数字 ID。"
UserNotOpenedWallet="❌ 用户未开通钱包。"
TransferFundsButton="💸 转移资金"
NoBalanceToTransfer="❌ 主账户没有任何余额可转移"
TransferFundsConfirmTitle="💸 确认资金转移"
TransferFundsConfirmMessage="您即将把 %s 账户的所有资金转移到 %s 账户"
AssetListTitle="📊 资产列表"
TransferFundsWarning="⚠️ 重要提示"
TransferFundsWarningMessage="此操作将转移所有资产且不可撤销！"
TransferFundsProcessingTitle="⏳ 正在处理"
TransferFundsProcessingMessage="正在转移资金，请稍候..."
TransferFundsFailedTitle="❌ 转移失败"
TransferFundsFailedMessage="资金转移失败：%s"
ContactSupportMessage="如需帮助，请联系客服"
TransferFundsSuccessTitle="✅ 转移成功"
TransferFundsSuccessMessage="所有资金已成功转移到您的账户！"
TransferFundsSystemError="系统错误，请稍后重试。"
TransferFundsAlreadyInProgress="资金转移正在进行中，请稍后再试。"

# 转账相关消息
transferAlreadyCollected = "该转账已被领取"

# Transfer Verification
TransferPasswordPromptDetails = "您正在进行转账\n金额: %s %s\n\n请输入支付密码:"
enterPasswordPrompt = "请输入支付密码："

ValidationAmountRequired = "金额是必填的"
ValidationAmountTooLong = "金额不能超过 10 位数字"
ValidationAmountInvalidChars = "金额只能包含数字"
ValidationAmountLeadingZero = "金额不能以 0 开头"
ValidationAmountTooSmall = "金额不能小于 0.001"
ValidationAmountTooLarge = "金额不能超过 1000000"
ValidationAmountInvalidFormat = "金额格式无效"
ValidationAmountZero = "金额不能为 0"
ValidationAmountNegative = "金额不能为负数"
ValidationAmountTooManyDecimals = "金额小数位数不能超过 %s 位"
ValidationAmountInvalid = "金额无效"
ValidationContextInvalid = "验证上下文无效"
ValidationSymbolRequired = "代币符号是必填的"
ValidationAmountInvalidValue = "金额数值无效"
ValidationAmountOverflow = "金额数值过大"
ValidationTokenNotFound = "代币未找到"
ValidationTokenInactive = "代币未激活"
ValidationTokenInvalidDecimals = "代币小数位配置无效"
ValidationAmountInconsistent = "金额数值不一致"
ErrorInvalidOrExpiredLink="❌ 无效或已过期的链接"
ErrorTransferAlreadyCompleted="❌ 转账已领取"
ErrorTransferAlreadyCancelled="❌ 转账已取消"
ErrorTransferAlreadyExpired="❌ 转账已过期"
ErrorTransferAlreadyFailed="❌ 转账已失败"
ErrorTransferAlreadyRefunded="❌ 转账已退款"

ErrorTransferPendingCollection="⏳ 转账待领取"
ErrorTransferPendingRefund="⏳ 转账待退款"
ErrorTransferPendingCreate="⏳ 转账待创建"
ErrorTransferPendingCancel="⏳ 转账待撤销"
ErrorTransferPendingExpire="⏳ 转账待过期"
ErrorTransferPendingFailed="⏳ 转账待失败"
ErrorTransferPendingRefunded="⏳ 转账待退款"
ErrorTransferExpired="⏳ 转账已过期"
ErrorTransferCancelled="❌ 转账已取消"
ErrorTransferFailed="❌ 转账已失败"
ErrorTransferRefunded="❌ 转账已退款"
ErrorTransferCreated="⏳ 转账待创建"
ErrorTransferCollected="✅ 转账已领取"

# Username requirement message
PleaseSetUsernameFirst = '''🔧 <b>需要设置用户名</b>

您好！要使用此机器人，您需要先设置 Telegram 用户名。

<b>📝 如何设置用户名：</b>
1. 打开 Telegram 设置
2. 进入"编辑个人资料"
3. 设置一个唯一的用户名（例如：@您的名字）
4. 保存更改
5. 返回并再次发送 /start

<b>💡 为什么需要用户名？</b>
您的用户名帮助我们安全地识别您的身份，并启用转账和支付等功能。

<i>设置用户名后，请使用 /start 重新启动机器人以继续！</i>'''

# 转账相关消息
TransferSuccess = "✅ 转账成功"
TransferFailed = "❌ 转账失败"
TransferRefunded = "❌ 转账已退款"
TransferExpired = "⏳ 转账已过期"


StartBot = "🚀 启动机器人"
GetFlow = "💰 资金流水"
Support = "👨‍💼 客服支持"

# 菜单命令
DepositCommand = "💰 充值"
WithdrawCommand = "💸 提现"
RedPacketCommand = "🧧 红包"
TransferCommand = "💫 转账"
ProfileCommand = "👤 个人中心"

# 回复键盘欢迎信息
WelcomeReplyKeyboard = "欢迎使用 xpay bot！"

# 客服支持相关
SupportCenterText = '''👋 您好，%s！

🔹 如需帮助，可以通过以下方式联系我们：

💁 在线客服时间：每天 24小时

请选择您需要的服务：'''

SupportLiveChatButton = "💬 在线客服"
SupportEmailButton = "📧 邮件支持"

# 帮助中心相关
HelpCenterText = '''🤖 <b>帮助中心</b>

<b>📋 可用命令：</b>
/start - 启动机器人并访问主菜单
/help - 显示此帮助信息
/support - 联系客服团队
/privacy - 查看隐私政策

<b>🚀 主要功能：</b>
💰 钱包管理
📤 发送和接收转账
🎁 红包功能
📊 交易历史
⚙️ 设置和个人资料

<b>⚡ 快速操作：</b>
• 使用主菜单按钮轻松导航
• 随时查看您的余额
• 即时向朋友转账
• 创建和分享红包

<b>🆘 需要更多帮助？</b>
使用 /support 直接联系我们的客服团队。

<i>最后更新：2024</i>'''

# 隐私政策相关
PrivacyPolicyText = '''🔒 <b>隐私政策</b>

<b>最后更新：</b> 2024年12月

<b>📋 我们收集的信息：</b>
• Telegram 用户ID和用户名
• 您账户的交易历史
• 钱包地址和余额
• 通信偏好设置

<b>🛡️ 我们如何保护您的数据：</b>
• 所有交易端到端加密
• 采用行业标准协议安全存储
• 定期安全审计和更新
• 未经同意不与第三方共享

<b>📊 我们如何使用您的信息：</b>
• 处理交易和转账
• 提供客户支持
• 改进我们的服务
• 遵守法律要求

<b>🔐 您的权利：</b>
• 访问您的个人数据
• 请求数据更正或删除
• 随时撤回同意
• 导出您的交易历史

<b>🍪 Cookie和跟踪：</b>
我们在此Telegram机器人中不使用Cookie或跟踪技术。

<b>📞 数据保留：</b>
• 交易记录：7年（法律要求）
• 个人数据：直到账户删除
• 客服对话：2年

<b>🌍 国际传输：</b>
您的数据可能在我们服务器所在的不同国家进行处理，始终采用适当的保护措施。

<b>📧 联系我们：</b>
隐私相关问题请联系：<code>%s</code>

<i>使用此机器人即表示您同意本隐私政策。</i>'''

# Unified Notifications (Key format: notification.event_name)
NotificationPaymentSent = "✅ 您已成功向 %s 支付 %s %s。"
NotificationPaymentReceived = "✅ 您已收到来自 %s 的 %s %s。"
NotificationDirectInvite = "🎉 您直接邀请的用户 %s 【%d】已注册成功!"
NotificationIndirectInvite = "🎊 您间接邀请的用户 %s 【%d】已注册成功!"
NotificationDepositSuccess = "✅ 恭喜！您的 %s %s 充值已成功到账。"
NotificationTransferExpired = "⏱️ 提醒：您向 %s 发起的 %s %s 转账请求已过期。"
NotificationRedPacketExpired = "⏱️ 提醒：您的红包 (ID: %s) 已过期，剩余金额 %s %s 已退回。"
NotificationRedPacketClaimedFully = "🎉 恭喜！您的红包 (ID: %s，总额 %s %s) 已被全部领取完毕！共 %d 人领取。"
# Withdrawal notification templates (following documentation requirements)
NotificationWithdrawalSuccess = "✅成功取款金额：%s，来自TXPay"
NotificationWithdrawalFailed = "❌取款失败，请您重新尝试\n\n原因：%s"

# Channel-specific withdrawal success notifications
NotificationWithdrawalSuccessTXPayCNY = "✅成功取款金额：%s，来自TXPay"
NotificationWithdrawalSuccessTXPayUSDT = "✅成功取款金额：%s，来自TXPay"
NotificationWithdrawalSuccessUSDTTRC20 = "✅成功取款金额：%s，来自TXPay"

# Channel-specific withdrawal failure notifications  
NotificationWithdrawalFailedTXPayCNY = "❌取款失败，请您重新尝试\n\n原因：%s"
NotificationWithdrawalFailedTXPayUSDT = "❌取款失败，请您重新尝试\n\n原因：%s"
NotificationWithdrawalFailedUSDTTRC20 = "❌取款失败，请您重新尝试\n\n原因：%s"

# Status headers for updating original withdrawal messages
WithdrawalStatusSuccess = "✅提现成功"
WithdrawalStatusFailed = "❌提现失败"

"system.error.insufficientFunds" = "❌ 余额不足"

# 收款请求相关消息
paymentRequestStatusPaid = "已经付款"
paymentRequestStatusPending = "待支付"
paymentRequestStatusExpired = "已过期"
paymentRequestStatusCancelled = "已取消"
paymentRequestAlreadyPaid = "该收款请求已被付款"
immediatePaymentButton = "立即支付"
paymentRequestMessagePending = "%s 发起了一笔 %s %s 的收款请求，请支付。"
paymentRequestMessagePaid = "%s 已向 %s 支付了 %s %s。"
paymentRequestMessageExpired = "%s 发起的 %s %s 收款请求已过期。"
paymentRequestMessageCancelled = "%s 发起的 %s %s 收款请求已取消。"
paymentRequestMessageUnknownStatus = "收款请求 %s 状态异常: %s"
paymentRequestMemoLabel = "备注: %s"

RedPacketMaxAmountError = "❌ 单个红包金额不能超过 %s。"

# CNY 提现相关
CNYWithdrawTitle = "💸 CNY 提现"
CNYWithdrawAmountPrompt = "请回复要提现的金额"
CNYWithdrawFeeInfo = "手续费: 金额 %.2f%% + %d CNY"
CNYWithdrawFeeExample = "例如: 提现 100 CNY\n手续费: 100 * %.2f%% + %d = %s CNY\n实际到账: 100 - %s = %s CNY"
CNYWithdrawPaymentTime = "💡 付款时间(每日): %s"
CNYWithdrawMethodSelection = "请选择提现方式："
CNYWithdrawWechatQR = "💰 微信二维码"
CNYWithdrawAlipayQR = "💰 支付宝二维码"
CNYWithdrawAlipayAccount = "💰 支付宝账号"
CNYWithdrawUploadWechatQR = "请上传您的微信收款二维码图片："
CNYWithdrawUploadAlipayQR = "请上传您的支付宝收款二维码图片："
CNYWithdrawEnterAlipayAccount = "请输入支付宝账号："
CNYWithdrawSupplementInfo = "请输入补充信息如：姓名, 有时二维码需要验证姓名信息！"
CNYWithdrawConfirmData = "✅ 请确认数据"
CNYWithdrawAmount = "提现金额: %s CNY"
CNYWithdrawMethod = "提现方式: %s"
CNYWithdrawAccount = "提现账号: %s"
CNYWithdrawActualAmount = "实际到账: %s CNY"
CNYWithdrawMinAmount = "最小提现金额: %s CNY"
CNYWithdrawMaxAmount = "最大提现金额: %s CNY"
CNYWithdrawCurrentBalance = "当前余额: %s"
CNYWithdrawUploadError = "获取图片失败，请重试"
CNYWithdrawImageSizeError = "图片大小不能超过 2MB"
CNYWithdrawImageFormatError = "无效的图片格式，请上传 JPG 或 PNG 格式的图片"
CNYWithdrawImageProcessError = "处理图片失败，请重试"
CNYWithdrawImageUploadError = "上传图片失败，请重试"
CNYWithdrawNameLengthError = "姓名长度应在 2-50 个字符之间"
CNYWithdrawAccountLengthError = "账号信息长度应在 5-100 个字符之间"
CNYWithdrawPleaseUploadImage = "请上传二维码图片"
CNYWithdrawInputPlaceholderAmount = "100"
CNYWithdrawInputPlaceholderName = "姓名"
CNYWithdrawInputPlaceholderAccount = "支付宝账号 姓名"
CNYWithdrawEnterName = "请输入收款人姓名："
CNYWithdrawSummary = "提现信息"
CNYWithdrawMethodLabel = "提现方式"
CNYWithdrawAccountLabel = "支付宝账号"
CNYWithdrawRecipientName = "收款人姓名: %s"

# 提现通用错误信息
WithdrawSessionExpired = "❌ 提现会话已过期，请重新开始"
WithdrawSessionConflict = "⚠️ 检测到操作冲突，提现会话已失效。\n\n这通常是因为您在输入密码期间进行了其他操作。\n\n请重新开始提现操作。"
WithdrawRetryButton = "🔄 重新开始提现"
WithdrawInvalidAmount = "❌ 金额格式无效"
WithdrawInvalidAmountDetail = "请输入有效的数字金额"
WithdrawDisabled = "❌ 提现功能已关闭"
WithdrawDisabledDetail = "系统维护中，请稍后再试"
WithdrawTokenDisabled = "❌ 该币种暂停提现"
WithdrawTokenDisabledDetail = "CNY 提现功能暂时不可用"
WithdrawConfigError = "❌ 配置错误"
WithdrawConfigErrorDetail = "系统配置异常，请联系客服"
UserNotFoundDetail = "无法获取用户信息"
WalletNotFound = "❌ 钱包未找到"
WalletNotFoundDetail = "无法获取钱包信息"
BalanceCheckFailed = "❌ 余额查询失败"
BalanceCheckFailedDetail = "无法查询您的余额，请稍后再试"
SystemErrorDetail = "系统处理异常，请稍后再试"
TransferFundsPasswordTitle="请输入支付密码完成资金转移"
TransferFundsPasswordMessage="您即将把 %s 账户的所有资金转移到 %s 账户，请输入支付密码以确认。"

# 红包封面审核通知
NotificationRedPacketCoverApproved = "✅ 您的红包封面已通过审核！现在可以在创建红包时使用。"
NotificationRedPacketCoverRejected = "❌ 您的红包封面审核未通过。\n\n拒绝原因：%s"

# 闪兑功能
SwapTitle = "🔄 代币闪兑"
SwapSelectFromToken = "请选择您要兑换的代币："
SwapSelectToToken = "请选择您要兑换到的代币："
SwapEnterAmount = "请输入您要%s的 %s 数量（用于兑换 %s）："
SwapEstimatedReceive = "预计收到：%s %s"
SwapExchangeRate = "汇率：1 %s = %s %s"
SwapMinAmount = "最小兑换金额：%s %s"
SwapMinAmountQuote = "最小支付金额：%s %s"
SwapMaxAmount = "最大兑换金额：%s %s"
SwapMaxAmountQuote = "最大支付金额：%s %s"
SwapConfirmTitle = "请确认您的兑换："
SwapFromAmount = "从：%s %s"
SwapToAmount = "到：%s %s（预估）"
SwapFee = "手续费：%s %s"
SwapConfirmButton = "✅ 确认兑换"
SwapCancelButton = "❌ 取消"
SwapEnterPassword = "🔑 请输入支付密码以完成兑换："
SwapSuccess = "✅ 兑换成功！\n\n兑换：%s %s\n收到：%s %s\n汇率：%s\n手续费：%s %s\n订单号：%s"
SwapFailed = "❌ 兑换失败：%s"
SwapCancelled = "❌ 兑换已取消。"
SwapSessionConflict = "⚠️ 检测到操作冲突，闪兑会话已失效。\n\n这通常是因为您在输入密码期间进行了其他操作。\n\n请重新开始闪兑操作。"
SwapRetryButton = "🔄 重新开始闪兑"
SwapCurrentPrice = "💹 当前价格：1 %s = %s %s"
SwapCNYBuyPrice = "💹 买入价格：1 %s = %s %s"
SwapCNYSellPrice = "💹 卖出价格：1 %s = %s %s"
SwapCNYReverseBuyPrice = "💱 参考：1 %s = %s %s"
SwapCNYReverseSellPrice = "💱 参考：1 %s = %s %s"
SwapPriceUnavailable = "💹 价格：市场价"
SwapInsufficientBalance = "❌ 余额不足。您的可用余额为 %s %s。"
SwapAmountTooSmall = "❌ 金额太小。最小兑换金额为 %s %s。"
SwapAmountTooLarge = "❌ 金额太大。最大兑换金额为 %s %s。"
SwapInvalidAmount = "❌ 无效的金额格式。请输入一个有效的数字。"
SwapRateUnavailable = "❌ 汇率不可用。请稍后再试。"
SwapSameToken = "❌ 不能兑换相同的代币。"
SwapFeatureDisabled = "❌ 闪兑功能目前已关闭。"
SwapNoPermission = "❌ 您没有使用闪兑功能的权限。"
SwapPasswordIncorrect = "❌ 支付密码错误。请重试。"
SwapNoPaymentPassword = "❌ 请先设置支付密码才能使用闪兑功能。"
SwapProcessing = "⏳ 正在处理您的兑换..."
SwapRateExpired = "⏱️ 汇率已过期。请重试。"
SwapServiceUnavailable = "❌ 闪兑服务暂时不可用。"
SwapUnknownError = "❌ 兑换过程中发生未知错误。"
SwapHistory = "🔄 兑换历史"
SwapHistoryTitle = "📊 兑换历史"
SwapHistoryEmpty = "暂无兑换记录。"
SwapHistoryStartSwap = "🔄 开始兑换"
SwapHistoryBackToProfile = "👤 返回个人中心"
SwapOrderBack = "⬅️ 返回"
SwapOrderMainMenu = "🏠 主菜单"
SwapHistoryOrderStatus = "状态"
SwapHistoryFrom = "兑换"
SwapHistoryTo = "收到"
SwapHistoryTime = "时间"
SwapHistoryViewDetails = "📋 查看详情"
SwapHistoryPage = "第 %d 页 / 共 %d 页"
SwapHistoryCurrentPage = "第 %d 页"
SwapHistoryPreviousPage = "⬅️ 上一页"
SwapHistoryNextPage = "➡️ 下一页"
SwapPairNotSupported = "❌ 不支持该代币对的兑换。"
SwapSlippageWarning = "⚠️ 由于市场波动，实际金额可能会有所不同。"

# 额外的兑换翻译
SwapEnterPaymentPassword = "🔐 请输入支付密码以确认兑换："
SwapUseTextInput = "请以文本消息形式输入金额。"
SwapErrorLoadingHistory = "❌ 加载兑换历史记录出错，请重试。"
SwapStart = "🔄 开始新的兑换"
SwapOrder = "订单"
SwapNewOrder = "📝 新的兑换"
SwapOrderNotFound = "❌ 未找到订单。"
SwapOrderDetails = "📋 兑换订单详情\n\n订单号：%s\n创建时间：%s\n状态：%s\n交易类型：%s\n\n兑出：%s %s\n兑入：%s %s\n汇率：%s\n手续费：%s %s"
SwapCompletedAt = "完成时间"
SwapCreatedAt = "创建时间"
SwapActualPrice = "实际汇率：%s"
SwapFailureReason = "失败原因：%s"
SwapTransactionHash = "交易哈希"
SwapOrderStatusPending = "⏳ 待处理"
SwapOrderStatusProcessing = "⚙️ 处理中"
SwapOrderStatusCompleted = "✅ 已完成"
SwapOrderStatusFailed = "❌ 失败"
SwapOrderStatusCancelled = "🚫 已取消"
SwapOrderStatusUnknown = "❓ 未知"

# 通用翻译
FeatureNotImplemented = "🚧 此功能尚未实现。"
InvalidRequest = "❌ 无效的请求。"
SessionExpired = "⏱️ 会话已过期，请重试。"
MainMenu = "🏠 主菜单"
SwapSelectProduct = "🔄 选择要兑换的交易对："
SwapNoProductsAvailable="❌ 暂无可用的交易对。"
SwapProductSelected = "✅ 已选择 %s"
SwapSelectDirection = "请选择交易方向："
SwapBuy = "💰 买入 %s"
SwapSell = "💸 卖出 %s"
SwapActionBuy = "买入"
SwapActionSell = "卖出"
SwapEnterBuyAmount = "💸 请输入您要支付的 %s 数量（用于买入 %s）："
SwapEnterSellAmount = "💰 请输入您要卖出的 %s 数量（将获得 %s）："
SwapBalance = "💰 您的余额：%s %s"
SwapAgain = "🔄 再次兑换"
SwapExecutionError = "❌ 兑换执行失败：%s"
SwapInsufficientBalanceError = "❌ 余额不足，无法完成兑换"
SwapOrderError = "❌ 创建订单失败：%s"
SwapQuoteDetails = "📊 兑换报价详情\n\n💱 兑换：%s %s\n📈 预计收到：%s %s\n💹 当前价格：1 %s = %s %s\n💰 预估手续费：%s %s"
SwapQuoteError = "❌ 获取报价失败：%s"
SwapAmountBelowMinimum = "❌ 输入金额太小\n\n最小兑换金额：%s %s\n请输入大于或等于最小金额的数量"
SwapAmountBelowMinimumQuote = "❌ 输入金额太小\n\n最小支付金额：%s %s\n请输入大于或等于最小金额的数量"
SwapAmountExceedsMaximum = "❌ 输入金额太大\n\n最大兑换金额：%s %s\n请输入小于或等于最大金额的数量"
SwapAmountExceedsMaximumQuote = "❌ 输入金额太大\n\n最大支付金额：%s %s\n请输入小于或等于最大金额的数量"
SwapQuoteExpired = "⏱️ 报价已过期"
SwapQuoteExpiredHint = "报价有效期为2分钟，您的报价已经超时。请点击下方按钮重新获取最新报价。"
SwapSpread = "价差：%s%"
SwapPriceReminder = "💡 提示：成交价格以最新市场价格为准"
actualPrice = "实际价格"
cancel = "❌ 取消"
completedAt = "完成时间"
confirm = "✅ 确认"
failureReason = "失败原因"
featureNotImplemented = "🚧 功能暂未实现"
incorrectPassword = "❌ 密码错误"
invalidAmount = "❌ 无效金额"
invalidDirection = "❌ 无效的交易方向"
invalidPasswordFormat = "❌ 密码格式错误，请输入6位数字"
invalidProduct = "❌ 无效的产品"
invalidRequest = "❌ 无效的请求"
mainMenu = "🏠 主菜单"
newSwap = "🔄 新的兑换"
orderNotFound = "❌ 订单未找到"
orderStatusCancelled = "已取消"
orderStatusCompleted = "已完成"
orderStatusFailed = "失败"
orderStatusPending = "待处理"
orderStatusProcessing = "处理中"
SwapTradeTypeBuy = "买入"
SwapTradeTypeSell = "卖出"

# Swap Error Messages (Specific)
SwapErrorInsufficientBalance = "❌ 余额不足，无法完成兑换"
SwapErrorQuoteExpired = "⏱️ 报价已过期，请重新获取报价"
SwapErrorPriceSlippage = "📈 价格波动过大，请稍后重试"
SwapErrorAmountTooSmall = "❌ 兑换金额太小，请输入更大的金额"
SwapErrorAmountTooLarge = "❌ 兑换金额太大，请输入更小的金额"
SwapErrorAmountBelowMinimum = "❌ 金额低于最小限制 (%s %s)"
SwapErrorAmountExceedsMaximum = "❌ 金额超过最大限制 (%s %s)"
SwapErrorServiceUnavailable = "🔧 兑换服务暂时不可用，请稍后再试"
SwapErrorRateLimitExceeded = "⏳ 操作过于频繁，请稍后再试"
SwapErrorUserLimitExceeded = "📊 已达到您的兑换限额"
SwapErrorDailyLimitExceeded = "❌ 已超过每日兑换限额 ($%s)。请明天再试或使用更小的金额。"
SwapErrorPriceManipulation = "⚠️ 检测到异常价格，交易已被阻止"
SwapErrorAccountSuspended = "🔒 您的账户暂时无法使用兑换功能"
SwapErrorSecurityCheckFailed = "🛡️ 安全检查失败，请联系客服"
SwapErrorInternalError = "❌ 系统处理出错，请稍后重试"
SwapErrorBuyingNotAllowed = "❌ 该交易对暂时不支持买入操作"
SwapErrorSellingNotAllowed = "❌ 该交易对暂时不支持卖出操作"
SwapErrorTradingPairNotActive = "❌ 该交易对暂时不可用"
SwapErrorTradingPairNotFound = "❌ 不支持该货币对的兑换"
SwapErrorInvalidConfiguration = "❌ 交易配置错误，请联系客服"
order = "订单 %s"
processingSwap = "⏳ 正在处理兑换..."
productNotFound = "❌ 产品未找到"
serviceUnavailable = "❌ 服务暂时不可用"
sessionExpired = "⏱️ 会话已过期"
transactionHash = "交易订单：%s"
useTextInput = "📝 使用文本输入"
viewDetails = "📋 查看详情"
NoRedPacketCover="❌ 不设置封面"
RedPacketNoCoverNotice="无红包封面"
RedPacketEnterPasswordTitle="🔑 请输入支付密码以创建红包:"
RedPacketPasswordFailed="❌ 支付密码验证失败，请重试。"
RedPacketPasswordIncorrect="❌ 支付密码错误。请重试。"
RedPacketPasswordExpired="⏱️ 支付密码已过期，请重试。"
SingleAmountLabel = "💰 单个金额"
RedPacketPasswordRequired = "❗️ 创建红包需要支付密码。请先设置支付密码。"
RedPacketPasswordRequiredDetail = "为了您的资金安全，请先设置支付密码后再进行红包操作。"
RedPacketPasswordSet = "✅ 支付密码已设置。现在可以创建红包。"
paymentAmountExceedsMaximum = "❌ 支付金额超过最大限制。最大金额：%s %s"
paymentAmountBelowMinimum = "❌ 支付金额低于最小限制。最小金额：%s %s"
PassFreeAmountTooSmall = "❌ 金额太小，低于小额免密金额。最小金额：%s %s"
PassFreeAmountTooLarge = "❌ 金额太大，超过小额免密金额。最大金额：%s %s"
SwapErrorGlobalLimitExceeded = "❌ 系统每日兑换限额已达上限，请明日再试。"
SwapErrorGlobalDailyLimitExceeded = "❌ 系统每日兑换限额已达上限（$%s USD），请明日再试。"

# Withdraw Error Messages
WithdrawSelectSymbolOrChainFirst = "❌ 请先选择代币符号或网络"

# Rate Limit Messages
rateLimitExceeded = "⚠️ 您的操作过于频繁，请等待60秒后再试。"
rateLimitExceededCallback = "操作过于频繁，请稍后再试。"
rateLimitCountdown = "⏳ 您已被暂时限制。请等待 %d 秒后再试。"

# Commission Notification Messages
CommissionNotificationDirect = "💰 *佣金到账通知*\n\n恭喜！您的直推用户 %s 的游戏投注为您带来了 *%s %s* 佣金！\n\n💎 佣金类型：直接推荐\n📈 佣金比例：%s%%\n⏰ 到账时间：%s"
CommissionNotificationIndirect = "💰 *佣金到账通知*\n\n恭喜！您的间接推荐用户的游戏投注为您带来了 *%s %s* 佣金！\n\n💎 佣金类型：间接推荐\n📈 佣金比例：%s%%\n⏰ 到账时间：%s"
CommissionSummaryDaily = "📊 *每日佣金汇总*\n\n{.Date} 的佣金已结算完成：\n\n💰 总佣金：*{.TotalAmount} {.Symbol}*\n👥 直推佣金：{.DirectAmount} {.Symbol}\n🔗 间推佣金：{.IndirectAmount} {.Symbol}\n📝 交易笔数：{.Count}\n\n感谢您的推广支持！继续加油！🚀"

# Invite Friends Feature
InviteFriendsMenuTitle = "📨 邀请好友"
DirectSubordinatesButton = "👥 您的直接下级"
DirectSubordinatesTitle = "👥 您的直接下级用户"
NoSubordinatesMessage = "您还没有邀请任何用户。开始邀请好友，赚取佣金奖励！"
RegistrationTime = "注册时间"
UnknownUser = "未知用户"
DetailPrefix = ""
DetailSuffix = " 详情"
SubordinateDetailTitle = "👤 直接下级详情"
UserName = "用户名"
UserID = "用户ID"
DirectInvitesCount = "直接邀请人数"
IndirectInvitesCount = "间接邀请人数"
MonthlyStatisticsTitle = "📊 本月输赢统计"
TotalDeposits = "总存款"
TotalWithdrawals = "总取款"
CommissionEarningsTitle = "💰 佣金收益"
CommissionEarned = "已获得佣金"
CommissionEarnedWithRate = "已获得佣金: %s CNY (总投注的%.1f%%)"
OfTotalBets = "总投注的"
IndirectSubordinatesTitle = "👥 您的间接下级用户"
NoIndirectSubordinatesMessage = "您还没有间接下级用户。您的直接下级邀请的用户将会显示在这里。"
ReferredBy = "推荐人"
IndirectSubordinateDetailTitle = "👤 间接下级详情"

# PayBot Deposit Messages
ServiceUnavailable = "❌ 服务暂时不可用，请稍后再试。"
DepositAddressUnavailable = "❌ 暂时无法获取充值地址，请稍后再试。"
DepositUSDTTitle = "将 Tether USDT(TRC20) 发送到该地址。发送后，存款将自动记入账户。"
DepositAddressCopyHint = "👆点击地址复制"

# Okpay Deposit Flow
OkpayDepositUSDTPrompt = "无流水需求，随时可以提现\n👇 请发送要充值的金额，最低 %s USDT。\n（💰 存款通过 @Okpay 隐私钱包快捷支付）"
OkpayDepositCNYPrompt = "无流水需求，随时可以提现\n👇 请发送要充值的金额，最低 %s CNY。\n（💰 存款通过 @Okpay 隐私钱包快捷支付）"
OkpayDepositCancel = "❌ 操作已取消，请重新开始充值流程"
OkpayDepositAmountTooLow = "❌ 充值金额不得小于 %s，请重新输入金额"
OkpayDepositAmountTooHigh = "❌ 充值金额不得大于 %s，请重新输入金额"
OkpayDepositInvalidAmount = "❌ 请输入有效的数字金额（例如：100）"
OkpayDepositSystemBusy = "❌ 系统繁忙，请稍后再试"
OkpayDepositUserError = "❌ 用户信息异常，请联系客服"
OkpayDepositFeatureDisabled = "❌ 充值功能暂时维护中，请稍后再试"
OkpayDepositBalanceError = "❌ 账户余额异常，请联系客服"
OkpayDepositLimitError = "❌ 充值金额超出限制，请调整后重试"
OkpayDepositServiceUnavailable = "❌ 充值服务暂时不可用，请稍后再试"
CancelButton = "❌ 取消"

# Okpay Callback Messages
OkpayCallbackInvalidOrder = "❌ 订单信息无效，请重新发起充值"
OkpayCallbackSystemError = "❌ 系统繁忙，请稍后再试"


# Error messages
ErrorFetchBalance = "❌ 获取余额失败"
ErrorGettingUser = "❌ 获取用户信息失败"
ErrorInternal = "❌ 内部错误"
ErrorInvalidCallback = "❌ 无效的回调"
ErrorUnknownOperation = "❌ 未知操作"

# Address related
ManualAddress = "手动输入地址"
RecipientName = "收款人姓名"

# Token related
TokenInfoError = "❌ 获取代币信息失败"
tokenNotAllowReceive = "❌ %s 代币暂不支持收款"

# Withdrawal messages
WithdrawAddressChainMismatch = "❌ 地址链类型不匹配"
WithdrawAddressConfirmation = "请确认提现地址：%s"
WithdrawAddressNotFound = "❌ 地址不存在"
WithdrawCannotDetermineNetwork = "❌ 无法确定网络类型"
WithdrawErrorFetchingAddresses = "❌ 获取地址列表失败"
WithdrawInvalidAddressID = "❌ 无效的地址ID"
WithdrawInvalidAddressType = "❌ 无效的地址类型"
WithdrawInvalidStateForAction = "❌ 当前状态无法执行此操作"
WithdrawInvalidTokenOrChain = "❌ 无效的代币或链"
WithdrawPromptPasswordForAddress = "请输入支付密码以确认地址"
WithdrawSelectSymbolFirst = "❌ 请先选择币种"
WithdrawTokenNotFoundForChain = "❌ 该链不支持此代币"
WithdrawUseKeyboardPrompt = "请使用下方键盘输入支付密码"

# Amount validation
withdrawalAmountExceedsMaximum = "❌ 提现金额超过最大限额 (%s %s)"

# Admin Withdrawal Approval
AdminButtonWithdrawalApproval = "提现审核"
AdminButtonApprove = "批准"
AdminButtonReject = "拒绝"
AdminButtonRejectAndBan = "拒绝并封禁"
AdminButtonUserBetting = "用户投注记录"
AdminButtonUserAccounts = "用户账变记录"
AdminButtonBackToWithdrawal = "返回提现详情"
AdminUserBettingRecordsHeader = "用户投注记录"
AdminUserBettingRecordsError = "❌ 获取用户信息失败"
AdminUserBettingRecordsPlaceholder = "📊 该功能正在开发中，请稍后再试。\n\n未来将展示：\n• 投注时间\n• 游戏类型\n• 投注金额\n• 输赢结果\n• 盈亏统计"
AdminUserAccountChangesHeader = "用户账变记录"
AdminUserAccountChangesError = "❌ 获取用户信息失败"
AdminUserAccountChangesPlaceholder = "💰 该功能正在开发中，请稍后再试。\n\n未来将展示：\n• 变动时间\n• 变动类型\n• 变动金额\n• 账户余额\n• 相关订单"
AdminWithdrawalApprovalError = "❌ 获取待审核提现失败"
AdminNoPendingWithdrawals = "暂无待审核的提现申请"
AdminWithdrawalDetailsNoData = "❌ 提现详情不存在"
AdminNoWithdrawalsFound = "📭 暂无待审核的提现申请"
AdminWithdrawalQueueHeader = "待审核提现列表"
AdminWithdrawalQueueStats = "共 %d 条记录 | 第 %d 页"
AdminWithdrawalDetailsHeader = "提现详情"
AdminWithdrawalDetailsError = "❌ 获取提现详情失败"
AdminWithdrawalBeingProcessed = "⚠️ 此提现正在被管理员 %d 处理中"
AdminWithdrawalProcessingError = "❌ 开始处理提现失败"
AdminStartProcessing = "开始处理"
AdminApprove = "批准"
AdminReject = "拒绝"
AdminRejectAndBan = "拒绝并封禁"
AdminBackToQueue = "返回列表"
AdminChannelType = "通道类型"
AdminCurrency = "币种"
AdminProcessingPermissionDenied = "只有开始处理此提现的管理员才能操作"
AdminProcessingStarted = "已开始处理此提现申请"
AdminWithdrawAddress = "提现地址"
AdminPrevious = "上一页"
AdminNext = "下一页"
AdminBasicInfo = "基本信息"
AdminAmount = "金额"
AdminHandlingFee = "手续费"
AdminActualAmount = "实际到账"
AdminCreatedAt = "创建时间"
AdminRegisterTime = "注册时间"
AdminStatus = "状态"
AdminUserSuspended = "已停用"
AdminWithdrawalStats = "提现统计"
AdminTotalAmount = "总提现金额"
AdminAverageAmount = "平均提现金额"
AdminRiskIndicators = "风险指标"
AdminRiskLevel = "风险等级"
AdminRiskNewUser = "新用户（注册少于7天）"
AdminRiskLargeAmount = "大额提现"
AdminRiskHighFrequency = "高频提现"
AdminProcessingInfo = "处理信息"
AdminProcessedBy = "处理人"
AdminProcessingTime = "处理时间"
AdminRecipientInfo = "收款信息"
AdminAddress = "地址"
AdminRecipientName = "收款人"
AdminRecipientAccount = "收款账户"
AdminWithdrawalInfo = "提现信息"
AdminWithdrawalID = "提现ID"
AdminOrderNo = "订单号"
AdminPaybotOrderNo = "PayBot订单号"
AdminFinancialDetails = "财务明细"
AdminConvertedAmount = "折合金额"
AdminChan = "网络"
AdminFiatType = "法币类型"
AdminTimestamps = "时间记录"
AdminCheckedAt = "审核时间"
AdminProcessingAt = "处理时间"
AdminUserRemark = "用户备注"

# Withdrawal approval translations
AdminInvalidWithdrawalID = "❌ 无效的提现ID"
AdminWithdrawalApproved = "提现申请已批准"
AdminWithdrawalRejected = "提现申请已拒绝，资金已退回用户账户"
AdminWithdrawalRejectedAndBanned = "提现申请已拒绝，用户已被封禁"
AdminWithdrawalRejectReasonPrompt = "请输入拒绝原因："
AdminWithdrawalRejectBanReasonPrompt = "请输入拒绝并封禁的原因："
AdminWithdrawalRejectReasonEmpty = "❌ 拒绝原因不能为空"
AdminWithdrawalRejectionError = "❌ 拒绝提现失败，请稍后重试"
AdminInvalidPageNumber = "❌ 无效的页码"
AdminWithdrawalDetailsFormat = "📋 提现审核 (%d/%d)\n\n👤 用户ID: %d\n📝 收款人: %s\n💰 申请金额: %s\n📊 手续费: %s\n💳 实际到账: %s\n📍 地址: %s\n🕐 申请时间: %s"

# Platform Statistics translations
AdminPlatformStatsTitle = "平台统计数据"
AdminPlatformStatsSelectPeriod = "📊 请选择要查看的统计时间段："
AdminStatsPeriod = "统计周期"
AdminStatsPeriodToday = "今天"
AdminStatsPeriodYesterday = "昨天"
AdminStatsPeriodThisWeek = "本周"
AdminStatsPeriodThisMonth = "本月"
AdminStatsPeriodLastMonth = "上月"
AdminStatsToday = "📅 今天"
AdminStatsYesterday = "📅 昨天"
AdminStatsThisWeek = "📅 本周"
AdminStatsThisMonth = "📅 本月"
AdminStatsLastMonth = "📅 上月"
AdminStatsExport = "📤 导出数据"
AdminStatsRefresh = "🔄 刷新"
AdminStatsUserSection = "用户统计"
AdminStatsTotalUsers = "总用户数"
AdminStatsNewUsers = "新增用户"
AdminStatsActiveUsers = "活跃用户"
AdminStatsFinancialSection = "财务统计"
AdminStatsTotalDeposits = "总充值"
AdminStatsTotalWithdrawals = "总提现"
AdminStatsNetRevenue = "净收入"
AdminStatsManualAdjustments = "手动调整"
AdminStatsBalanceAdjustments = "余额调整"
AdminStatsBonusAdjustments = "奖金调整"
AdminStatsGameSection = "游戏统计"
AdminStatsGameProfit = "游戏盈利"
AdminStatsGameLoss = "游戏亏损"
AdminStatsGameNetProfit = "游戏净利润"
AdminStatsCommissionSection = "佣金统计"
AdminStatsTotalCommission = "总佣金"
AdminStatsNote = "数据会定期缓存，最新数据可能有延迟"
AdminStatsRefreshing = "正在刷新数据..."
AdminStatsExportNotImplemented = "导出功能暂未实现"

# Betting Records
AdminBettingRecordsHeader = "投注记录"
AdminBettingRecordsError = "获取投注记录失败"
AdminBettingSummary = "投注统计"
AdminTotalBets = "总投注"
AdminTotalWins = "总赢取"
AdminNetResult = "净结果"
AdminTotalRebate = "总返水"
AdminWinRate = "胜率"
AdminBettingRecordsList = "投注记录列表"
AdminGameType = "游戏类型"
AdminProvider = "提供商"
AdminBetAmount = "投注金额"
AdminWinAmount = "赢取金额"
AdminNetAmount = "净金额"
AdminRebate = "返水"
AdminBetTime = "投注时间"
AdminNoBettingRecords = "暂无投注记录"
AdminButtonBettingRecords = "投注记录"
AdminButtonToday = "今天"
AdminButtonYesterday = "昨天"
AdminButtonThisWeek = "本周"
AdminButtonThisMonth = "本月"
AdminButtonAllRecords = "全部记录"
AdminPeriod = "时间段"

# Account Changes
AdminAccountChangesHeader = "账户变动记录"
AdminAccountChangesError = "获取账户变动记录失败"
AdminAccountSummary = "账户变动统计"
AdminNetChange = "净变动"
AdminCategoryBreakdown = "分类统计"
AdminAccountChangesList = "账户变动列表"
AdminBalance = "余额"
AdminRelatedUser = "相关用户"
AdminMemo = "备注"
AdminTime = "时间"
AdminNoAccountChanges = "暂无账户变动记录"
AdminButtonAccountChanges = "账户变动"
AdminButtonDeposit = "充值"
AdminButtonWithdrawal = "提现"
AdminButtonBonus = "奖金"
AdminButtonCommission = "佣金"
AdminButtonTransfer = "转账"
AdminButtonSystem = "系统"
AdminButtonAllCategories = "全部分类"
AdminButtonAllTime = "全部时间"
AdminCategory = "分类"
AdminButtonUserStats = "用户统计"

# Account Categories
AdminAccountCategoryDeposit = "充值"
AdminAccountCategoryWithdrawal = "提现"
AdminAccountCategoryTransferIn = "转入"
AdminAccountCategoryTransferOut = "转出"
AdminAccountCategoryBonus = "奖金"
AdminAccountCategoryCommission = "佣金"
AdminAccountCategoryRebate = "返水"
AdminAccountCategorySystemAdjust = "系统调整"
AdminAccountCategoryGameBet = "游戏投注"
AdminAccountCategoryGameWin = "游戏赢取"
AdminAccountCategoryRedPacketSend = "发红包"
AdminAccountCategoryRedPacketReceive = "收红包"

# Common (for betting/account records)
AdminPage = "页"
AdminTotalRecords = "总记录"
AdminTotalPages = "总页数"
AdminButtonPrevious = "上一页"
# Admin - Daily Reports
AdminDailyReportHeader = "日充提报表"
AdminDailyReportDescription = "查看每日充值和提现的汇总报表，支持自定义日期范围查询。"
AdminDailyReportInstructions = "请选择日期范围或使用快捷选项生成报表。"
AdminDateRange = "日期范围"
AdminStartDate = "开始日期"
AdminEndDate = "结束日期"
AdminNotSet = "未设置"
AdminButtonSetStartDate = "设置开始日期"
AdminButtonSetEndDate = "设置结束日期"
AdminButtonGenerateReport = "生成报表"
AdminButtonClearDates = "清除日期"
AdminButtonBackToDateSelection = "返回日期选择"
AdminDailyReportInputStartTitle = "设置开始日期"
AdminDailyReportInputStartPrompt = "请输入开始日期，格式为 YYYY-MM-DD"
AdminDailyReportInputEndTitle = "设置结束日期"
AdminDailyReportInputEndPrompt = "请输入结束日期，格式为 YYYY-MM-DD"
AdminExample = "示例"
AdminDailyReportNeedDates = "请先设置开始日期和结束日期"
AdminDailyReportInvalidFormat = "日期格式不正确，请使用 YYYY-MM-DD 格式"
AdminDailyReportError = "生成报表时出错，请稍后重试"
AdminDailyReportResultHeader = "日充提报表结果"

# Admin - Deposit Logs
AdminDepositLogsError = "获取充值记录失败"
AdminNoDepositLogs = "暂无充值记录"
AdminTotalDeposits = "总充值"
AdminUserDepositLogs = "用户充值记录"
AdminButtonBackToLogs = "返回记录列表"

# Admin - Withdrawal Logs
AdminWithdrawLogsError = "获取提现记录失败"
AdminNoWithdrawLogs = "暂无提现记录"
AdminUserWithdrawLogs = "用户提现记录"
AdminFailureReason = "失败原因"
AdminRemark = "备注"
AdminInvalidPage = "无效的页码"

# Admin - Merchant Info (New)
AdminMerchantInfoHeader = "商户信息"
AdminMerchantName = "商户名称"
AdminTenantID = "租户ID"
AdminBalanceStatistics = "余额统计"
AdminWalletBalance = "钱包余额"
AdminGameBalance = "游戏余额"
AdminTotalBalance = "总余额"
AdminWithdrawalFees = "提现手续费统计"
AdminTodayFees = "今日手续费"
AdminThisWeekFees = "本周手续费"
AdminThisMonthFees = "本月手续费"
AdminTotalFees = "总手续费"
AdminUserStatistics = "用户统计"
AdminTotalUsers = "总用户数"
AdminActiveUsers = "活跃用户"
AdminBannedUsers = "禁用用户"
AdminLastUpdate = "最后更新"
AdminFlowInfo= "流水信息"
AdminFlowStatistics = "流水统计"
AdminFlowToday = "今日流水"
AdminFlowThisWeek = "本周流水"
AdminFlowThisMonth = "本月流水"
AdminFlowTotal = "总流水"
AdminFlowBreakdown = "流水明细"
AdminFlowDeposit = "充值"
AdminFlowWithdrawal = "提现"
AdminFlowBet = "投注"
AdminFlowWin = "赢取"
AdminFlowNet = "净流水"

# Red Packet Additional Conditions
"(仅限 Premium会员领取)" = "(仅限 Premium会员领取)"
"(指定群组条件红包)" = "(指定群组条件红包)"
"流水要求" = "流水要求"
"(指定群组流水)" = "(指定群组流水)"
"流水时间" = "流水时间"
"流水金额" = "流水金额"
"天" = "天"
"(%d天)" = "(%d天)"
"(需要验证码)" = "(需要验证码)"
"今日" = "今日"
"近7天" = "近7天"
"近30天" = "近30天"
"本月" = "本月"
"总流水" = "总流水"
"近%d天" = "近%d天"
"需要%s流水达到%s %s" = "需要%s流水达到%s %s"


CreateRedPacketFailed = "❌ 创建红包失败"

# Group Red Packet Error Messages
GroupRedPacketAvgAmountTooLow = "单个红包平均金额不能少于 %s %s"
GroupRedPacketSingleAmountTooLow = "单个红包金额不能少于 %s %s"
GroupRedPacketQuantityExceeded = "群组红包个数不能超过 %d 个"
GroupRedPacketQuantityTooLow = "红包个数至少为 %d 个"
GroupRedPacketAmountZero = "红包总金额必须大于 0"
GroupRedPacketInsufficientBalance = "余额不足，无法创建红包"
GroupRedPacketInsufficientBalanceDetailed = "余额不足：当前余额 %s %s，需要 %s %s"
GroupRedPacketDailyLimitReached = "今日创建红包次数已达上限"
GroupRedPacketDailyAmountLimitReached = "今日创建红包金额已达上限"
GroupRedPacketRiskScoreTooHigh = "风险评分过高，暂时无法创建红包"
GroupRedPacketTemporarilyRestricted = "您已被暂时限制创建红包"
GroupRedPacketClaimRestricted = "您已被暂时限制领取红包"
GroupRedPacketDailyClaimLimitReached = "今日领取红包次数已达上限"
GroupRedPacketFailedAttemptsTooMany = "失败尝试次数过多，请稍后重试"
GroupRedPacketSystemError = "红包创建失败，请稍后重试"

# Group Red Packet Command Errors
GroupRedPacketInvalidFormat = "命令格式错误，正确格式：hb 金额CNY 个数 [祝福语] 或 hb 流水金额 金额CNY 个数 [祝福语]"
GroupRedPacketUnsupportedCommand = "不支持的命令类型，请使用 hb 或 xyhb"
GroupRedPacketInvalidAmount = "金额格式错误，请输入有效的数字"
GroupRedPacketAmountMustBePositive = "金额必须大于0"
GroupRedPacketInvalidQuantity = "红包个数格式错误，请输入有效的整数"
GroupRedPacketQuantityMustBePositive = "红包个数必须大于0"
GroupRedPacketUnsupportedToken = "不支持的币种：%s"
GroupRedPacketTokenNotEnabled = "币种 %s 暂不支持红包功能"
GroupRedPacketAmountTooSmall = "单个红包金额不能少于 %s %s"

# Group Red Packet Errors
GroupRedPacketUserNotRegistered = "用户信息获取失败，请先私聊机器人进行注册"
GroupRedPacketTokenInfoFailed = "代币信息获取失败"
GroupRedPacketConfigError = "系统配置错误，请联系管理员"
GroupRedPacketCreatedSuccess = "群组红包创建成功！"
GroupRedPacketAmount = "金额"
GroupRedPacketQuantity = "数量"
GroupRedPacketType = "类型"
GroupRedPacketBlessing = "祝福"
GroupRedPacketId = "红包ID"
GroupRedPacketUnit = "个"
GroupRedPacketTypeNormal = "普通红包"
GroupRedPacketTypeLucky = "幸运红包"
GroupRedPacketTypeUnknown = "未知类型"
GroupRedPacketTypeBettingVolume = "流水红包"
GroupRedPacketBettingVolume = "流水要求"
GroupRedPacketBettingVolumeFormat = "%s %s"
GroupRedPacketInvalidBettingVolume = "流水金额格式错误"
GroupRedPacketBettingVolumeMustBePositive = "流水金额必须大于0"
