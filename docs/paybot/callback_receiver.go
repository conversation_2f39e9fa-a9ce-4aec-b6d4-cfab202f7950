//go:build ignore

package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"
)

// CallbackData 回调数据基础结构
type CallbackData struct {
	EventType   string      `json:"event_type"`
	OrderNo     string      `json:"order_no"`
	MerchantId  uint64      `json:"merchant_id"`
	Amount      string      `json:"amount"`
	Currency    string      `json:"currency"`
	TxHash      string      `json:"tx_hash"`
	CompletedAt string      `json:"completed_at"`
	Timestamp   int64       `json:"timestamp"`
	RawData     interface{} `json:"-"`
}

// WithdrawCallbackData 提现回调数据
type WithdrawCallbackData struct {
	EventType    string `json:"event_type"`
	OrderNo      string `json:"order_no"`
	MerchantId   uint64 `json:"merchant_id"`
	Amount       string `json:"amount"`
	Currency     string `json:"currency"`
	ActualAmount string `json:"actual_amount"`
	HandlingFee  string `json:"handling_fee"`
	TxHash       string `json:"tx_hash"`
	CompletedAt  string `json:"completed_at"`
	Timestamp    int64  `json:"timestamp"`
}

// DepositCallbackData 充值回调数据
type DepositCallbackData struct {
	EventType     string `json:"event_type"`
	OrderNo       string `json:"order_no"`
	MerchantId    uint64 `json:"merchant_id"`
	Amount        string `json:"amount"`
	Currency      string `json:"currency"`
	FromAddress   string `json:"from_address"`
	ToAddress     string `json:"to_address"`
	TxHash        string `json:"tx_hash"`
	Confirmations int    `json:"confirmations"`
	CompletedAt   string `json:"completed_at"`
	Timestamp     int64  `json:"timestamp"`
}

// Config 配置
type Config struct {
	Port          int
	APIKey        string // 旧格式，向后兼容
	SecretKey     string // 旧格式，向后兼容
	WebhookSecret string // 新格式，推荐使用
}

// Logger 日志记录器
type Logger struct {
	*log.Logger
}

// NewLogger 创建新的日志记录器
func NewLogger() *Logger {
	return &Logger{
		Logger: log.New(os.Stdout, "[CALLBACK] ", log.LstdFlags|log.Lmicroseconds),
	}
}

// Server 回调接收服务器
type Server struct {
	config *Config
	logger *Logger
}

// NewServer 创建新的服务器实例
func NewServer(config *Config) *Server {
	return &Server{
		config: config,
		logger: NewLogger(),
	}
}

// generateSignature 生成HMAC签名
func (s *Server) generateSignature(method, uri, timestamp, nonce, body string) string {
	signString := method + uri + timestamp + nonce + body

	// 优先使用 WebhookSecret，如果没有则使用 SecretKey（向后兼容）
	secret := s.config.WebhookSecret
	if secret == "" {
		secret = s.config.SecretKey
	}

	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(signString))
	signature := hex.EncodeToString(h.Sum(nil))

	return signature
}

// verifySignature 验证请求签名
func (s *Server) verifySignature(r *http.Request, body []byte) error {
	// 尝试新的 Webhook 头格式
	timestamp := r.Header.Get("X-Webhook-Timestamp")
	nonce := r.Header.Get("X-Webhook-Nonce")
	signature := r.Header.Get("X-Webhook-Signature")
	eventType := r.Header.Get("X-Webhook-Event")

	// 如果新格式的头不存在，尝试旧格式（向后兼容）
	if timestamp == "" || nonce == "" || signature == "" {
		apiKey := r.Header.Get("X-API-Key")
		timestamp = r.Header.Get("X-Timestamp")
		nonce = r.Header.Get("X-Nonce")
		signature = r.Header.Get("X-Signature")

		// 验证必要的头信息
		if apiKey == "" || timestamp == "" || nonce == "" || signature == "" {
			return fmt.Errorf("missing required headers")
		}

		// 验证API密钥（旧格式）
		if apiKey != s.config.APIKey {
			return fmt.Errorf("invalid API key")
		}
	} else {
		// 新格式，记录事件类型
		if eventType != "" {
			s.logger.Printf("Webhook Event Type: %s", eventType)
		}
	}

	// 生成期望的签名
	expectedSignature := s.generateSignature(r.Method, r.RequestURI, timestamp, nonce, string(body))

	// 比较签名
	if signature != expectedSignature {
		return fmt.Errorf("signature verification failed")
	}

	return nil
}

// handleCallback 处理回调请求
func (s *Server) handleCallback(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		s.logger.Printf("ERROR: Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// 记录请求信息
	s.logger.Printf("REQUEST: Method=%s, URI=%s, Headers=%v", r.Method, r.RequestURI, r.Header)
	s.logger.Printf("BODY: %s", string(body))

	// 验证签名
	if err := s.verifySignature(r, body); err != nil {
		s.logger.Printf("ERROR: Signature verification failed: %v", err)
		response := map[string]interface{}{
			"code":    401,
			"message": fmt.Sprintf("Signature verification failed: %v", err),
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusUnauthorized)
		json.NewEncoder(w).Encode(response)
		return
	}

	// 解析基础回调数据
	var baseData CallbackData
	if err := json.Unmarshal(body, &baseData); err != nil {
		s.logger.Printf("ERROR: Failed to parse callback data: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	// 根据事件类型解析具体数据
	switch baseData.EventType {
	case "withdraw_completed":
		var withdrawData WithdrawCallbackData
		if err := json.Unmarshal(body, &withdrawData); err != nil {
			s.logger.Printf("ERROR: Failed to parse withdraw data: %v", err)
			http.Error(w, "Invalid withdraw data format", http.StatusBadRequest)
			return
		}
		s.logger.Printf("WITHDRAW COMPLETED: OrderNo=%s, MerchantId=%d, Amount=%s %s, ActualAmount=%s, Fee=%s, TxHash=%s",
			withdrawData.OrderNo, withdrawData.MerchantId, withdrawData.Amount, withdrawData.Currency,
			withdrawData.ActualAmount, withdrawData.HandlingFee, withdrawData.TxHash)

	case "deposit_confirmed":
		var depositData DepositCallbackData
		if err := json.Unmarshal(body, &depositData); err != nil {
			s.logger.Printf("ERROR: Failed to parse deposit data: %v", err)
			http.Error(w, "Invalid deposit data format", http.StatusBadRequest)
			return
		}
		s.logger.Printf("DEPOSIT CONFIRMED: OrderNo=%s, MerchantId=%d, Amount=%s %s, From=%s, To=%s, TxHash=%s, Confirmations=%d",
			depositData.OrderNo, depositData.MerchantId, depositData.Amount, depositData.Currency,
			depositData.FromAddress, depositData.ToAddress, depositData.TxHash, depositData.Confirmations)

	default:
		s.logger.Printf("WARNING: Unknown event type: %s", baseData.EventType)
	}

	// 返回成功响应
	response := map[string]interface{}{
		"code":    0,
		"message": "success",
		"data": map[string]interface{}{
			"received_at": time.Now().Format(time.RFC3339),
			"order_no":    baseData.OrderNo,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)

	// 记录处理时间
	s.logger.Printf("PROCESSED: Order=%s, EventType=%s, Duration=%v", baseData.OrderNo, baseData.EventType, time.Since(startTime))
}

// handleHealth 健康检查端点
func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// Run 运行服务器
func (s *Server) Run() error {
	// 设置路由
	http.HandleFunc("/callback", s.handleCallback)
	http.HandleFunc("/health", s.handleHealth)

	// 启动服务器
	addr := fmt.Sprintf(":%d", s.config.Port)
	s.logger.Printf("Starting callback receiver server on %s", addr)

	if s.config.WebhookSecret != "" {
		s.logger.Printf("Using Webhook Secret: %s***%s", s.config.WebhookSecret[:8], s.config.WebhookSecret[len(s.config.WebhookSecret)-4:])
	} else {
		s.logger.Printf("API Key: %s", s.config.APIKey)
		s.logger.Printf("Secret Key: %s***%s", s.config.SecretKey[:4], s.config.SecretKey[len(s.config.SecretKey)-4:])
	}

	return http.ListenAndServe(addr, nil)
}

func main() {
	// 解析命令行参数
	var port int
	flag.IntVar(&port, "port", 8088, "服务器端口")
	flag.Parse()

	config := &Config{
		Port:          port,
		APIKey:        "MK5Tn99YdKVTi5_4n1rTfkpAkytul0tMPtOooB_4Ndc=",                                             // 旧格式（向后兼容）
		SecretKey:     "kVusrpPwwFr7jF1LkzgwcicFw6ZdVrcrt1q5yY4zAj_nOSEtuuwaOsN3QEddFLbIjVr-p1Oc6hSHziaWpMayaw==", // 旧格式（向后兼容）
		WebhookSecret: "whsec_U36zg11p3Abs1t0oDTyEpmhAX_eYyrCrpYYqYuRDOQYyXooGQbj59ZPwRMdy8nImkimw-8VyE9pepi7aqDmuEg==",
	}

	// 验证必要参数
	if config.WebhookSecret == "" && (config.APIKey == "" || config.SecretKey == "") {
		fmt.Fprintf(os.Stderr, "Error: Either WebhookSecret or both APIKey and SecretKey are required\n\n")
		flag.PrintDefaults()
		os.Exit(1)
	}

	// 创建并运行服务器
	server := NewServer(config)
	if err := server.Run(); err != nil {
		log.Fatalf("Server error: %v", err)
	}
}
